2025-07-03 14:51:42,463 - __main__ - INFO - ====================================================================================================
2025-07-03 14:51:42,463 - __main__ - INFO - OPRO系统启动
2025-07-03 14:51:42,463 - __main__ - INFO - ====================================================================================================
2025-07-03 14:51:42,463 - __main__ - INFO - 运行模式: integrated
2025-07-03 14:51:42,463 - __main__ - INFO - LLM提供商: zhipuai
2025-07-03 14:51:42,463 - __main__ - INFO - OPRO启用: True
2025-07-03 14:51:42,464 - __main__ - INFO - 初始化系统...
2025-07-03 14:51:42,464 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-03 14:51:42,565 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-03 14:51:42,565 - __main__ - INFO - 分析缓存初始化完成
2025-07-03 14:51:42,565 - __main__ - INFO - 联盟管理器初始化完成
2025-07-03 14:51:42,565 - __main__ - INFO - 交易模拟器初始化完成
2025-07-03 14:51:42,565 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-03 14:51:42,565 - __main__ - DEBUG - 所有核心模块初始化完成
2025-07-03 14:51:42,565 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-03 14:51:42,582 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-03 14:51:42,583 - __main__ - INFO - 数据库初始化完成
2025-07-03 14:51:42,583 - __main__ - DEBUG - 刷新Shapley值缓存...
2025-07-03 14:51:42,602 - __main__ - DEBUG - 缓存刷新完成，共加载 7 个智能体的数据
2025-07-03 14:51:42,602 - __main__ - INFO - 最新Shapley数据加载完成
2025-07-03 14:51:42,602 - __main__ - INFO - 历史得分管理器初始化完成
2025-07-03 14:51:42,602 - __main__ - INFO - OPRO优化器初始化完成
2025-07-03 14:51:42,602 - __main__ - INFO - OPRO组件初始化成功
2025-07-03 14:51:42,602 - __main__ - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-03 14:51:42,602 - __main__ - INFO - 系统初始化完成
2025-07-03 14:51:42,603 - __main__ - INFO - ================================================================================
2025-07-03 14:51:42,603 - __main__ - INFO - 运行模式: 集成模式（评估+优化）
2025-07-03 14:51:42,603 - __main__ - INFO - ================================================================================
2025-07-03 14:51:42,603 - __main__ - INFO - ============================================================
2025-07-03 14:51:42,603 - __main__ - INFO - 步骤2: 运行贡献度评估
2025-07-03 14:51:42,603 - __main__ - INFO - ============================================================
2025-07-03 14:51:42,603 - __main__ - INFO - 开始贡献度评估流程
2025-07-03 14:51:42,603 - __main__ - INFO - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-03 14:51:42,603 - __main__ - INFO - 可用智能体实例: 无（将使用模拟智能体）
2025-07-03 14:51:42,603 - __main__ - INFO - ==================================================
2025-07-03 14:51:42,603 - __main__ - INFO - 阶段1: 分析缓存
2025-07-03 14:51:42,603 - __main__ - INFO - 使用LLM直接分析...
2025-07-03 14:51:42,603 - __main__ - INFO - 开始LLM分析缓存阶段...
2025-07-03 14:51:42,603 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:51:42,603 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:51:42,603 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:51:42,604 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=8.0 socket_options=None
2025-07-03 14:51:42,605 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x14cb95310>
2025-07-03 14:51:42,605 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-07-03 14:51:42,605 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:51:42,605 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-07-03 14:51:42,605 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:51:42,605 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-03 14:51:42,606 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-03 14:51:42,606 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x14d0de350> server_hostname='open.bigmodel.cn' timeout=8.0
2025-07-03 14:51:42,925 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x14d105760>
2025-07-03 14:51:42,925 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:51:42,925 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:51:42,926 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:51:42,926 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:51:42,926 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:51:44,257 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:51:44 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117515255032723764e005f94d3662a4b12c4ff0aec3b773f94da18;path=/;HttpOnly;Max-Age=1800'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145143109cd151509642ca'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:51:44,258 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:51:44,258 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:51:44,258 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:51:44,258 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:51:44,259 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:51:44,259 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:51:44,260 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:51:44,260 - __main__ - INFO - LLM为 NAA 生成的分析已缓存
2025-07-03 14:51:44,260 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:51:44,260 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:51:44,260 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:51:44,261 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:51:44,261 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:51:44,261 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:51:44,261 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:51:44,261 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:51:46,870 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:51:47 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145144c81f6e64db364eee'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:51:46,870 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:51:46,871 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:51:46,871 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:51:46,871 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:51:46,872 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:51:46,872 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:51:46,874 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:51:46,874 - __main__ - INFO - LLM为 TAA 生成的分析已缓存
2025-07-03 14:51:46,874 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:51:46,874 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:51:46,874 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:51:46,874 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:51:46,874 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:51:46,874 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:51:46,874 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:51:46,874 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:51:48,252 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:51:48 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145147409cdacbfc3a4f3f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:51:48,253 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:51:48,253 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:51:48,254 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:51:48,254 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:51:48,254 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:51:48,254 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:51:48,255 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:51:48,255 - __main__ - INFO - LLM为 FAA 生成的分析已缓存
2025-07-03 14:51:48,255 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:51:48,255 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:51:48,255 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:51:48,255 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:51:48,256 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:51:48,256 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:51:48,256 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:51:48,256 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:51:53,964 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:51:54 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145148224f2a48344142fa'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:51:53,965 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:51:53,966 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:51:53,966 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:51:53,966 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:51:53,966 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:51:53,966 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:51:53,968 - __main__ - DEBUG - 已缓存智能体 BOA 的分析结果
2025-07-03 14:51:53,968 - __main__ - INFO - LLM为 BOA 生成的分析已缓存
2025-07-03 14:51:53,968 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:51:53,968 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:51:53,968 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:51:53,969 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:51:53,969 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:51:53,969 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:51:53,969 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:51:53,970 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:52:00,708 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:52:01 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145154c352e8c674df4b50'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:52:00,709 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:52:00,709 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:52:00,714 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:52:00,714 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:52:00,714 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:52:00,714 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:52:00,715 - __main__ - DEBUG - 已缓存智能体 BeOA 的分析结果
2025-07-03 14:52:00,716 - __main__ - INFO - LLM为 BeOA 生成的分析已缓存
2025-07-03 14:52:00,716 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:52:00,716 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:52:00,716 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:52:00,717 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:52:00,717 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:52:00,717 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:52:00,718 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:52:00,718 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:52:05,157 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:52:05 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031452010018ee46b70248f8'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:52:05,157 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:52:05,157 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:52:05,158 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:52:05,158 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:52:05,158 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:52:05,158 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:52:05,160 - __main__ - DEBUG - 已缓存智能体 NOA 的分析结果
2025-07-03 14:52:05,160 - __main__ - INFO - LLM为 NOA 生成的分析已缓存
2025-07-03 14:52:05,160 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:52:05,160 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:52:05,160 - zhipuai.api_resource.chat.completions - DEBUG - temperature:NOT_GIVEN, top_p:NOT_GIVEN
2025-07-03 14:52:05,161 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:52:05,161 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:52:05,162 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:52:05,162 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:52:05,162 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:52:14,698 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:52:14 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145205781140befd7d4e59'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:52:14,698 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:52:14,698 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:52:14,698 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:52:14,699 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:52:14,699 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:52:14,699 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:52:14,699 - __main__ - DEBUG - 已缓存智能体 TRA 的分析结果
2025-07-03 14:52:14,699 - __main__ - INFO - LLM为 TRA 生成的分析已缓存
2025-07-03 14:52:14,699 - __main__ - INFO - ==================================================
2025-07-03 14:52:14,699 - __main__ - INFO - 阶段2: 联盟生成与剪枝
2025-07-03 14:52:14,699 - __main__ - INFO - 开始联盟生成阶段...
2025-07-03 14:52:14,699 - __main__ - INFO - 开始联盟生成和剪枝
2025-07-03 14:52:14,699 - __main__ - INFO - 总智能体数: 7
2025-07-03 14:52:14,700 - __main__ - INFO - 分析智能体: {'NAA', 'FAA', 'TAA'}
2025-07-03 14:52:14,700 - __main__ - INFO - 交易智能体: TRA
2025-07-03 14:52:14,700 - __main__ - DEBUG - 生成了 128 个子集，智能体数量: 7
2025-07-03 14:52:14,700 - __main__ - INFO - 生成了 128 个初始联盟
2025-07-03 14:52:14,700 - __main__ - INFO - 联盟剪枝完成:
2025-07-03 14:52:14,700 - __main__ - INFO -   - 总联盟数: 128
2025-07-03 14:52:14,700 - __main__ - INFO -   - 有效联盟: 56
2025-07-03 14:52:14,700 - __main__ - INFO -   - 剪枝联盟: 72
2025-07-03 14:52:14,700 - __main__ - INFO -   - 剪枝效率: 56.2%
2025-07-03 14:52:14,700 - __main__ - INFO -   - 生成耗时: 0.000s
2025-07-03 14:52:14,700 - __main__ - DEBUG - 联盟结构分析完成: 56 个有效联盟
2025-07-03 14:52:14,700 - __main__ - INFO - 联盟生成阶段完成: 有效联盟 56 个，剪枝联盟 72 个
2025-07-03 14:52:14,700 - __main__ - INFO - ==================================================
2025-07-03 14:52:14,700 - __main__ - INFO - 阶段3: 交易模拟
2025-07-03 14:52:14,700 - __main__ - INFO - 开始交易模拟阶段...
2025-07-03 14:52:14,700 - __main__ - INFO - 启用并发模拟：56 个联盟，最大并发数：30
2025-07-03 14:52:14,700 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:14,701 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:14,701 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:14,701 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'BOA'}
2025-07-03 14:52:14,701 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:14,701 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'FAA', 'TAA'}
2025-07-03 14:52:14,701 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'BOA'}
2025-07-03 14:52:14,701 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'NOA'}
2025-07-03 14:52:14,701 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:14,702 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'FAA', 'TAA'}
2025-07-03 14:52:14,702 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:14,702 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'NOA'}
2025-07-03 14:52:14,702 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TRA', 'NOA', 'BeOA'}
2025-07-03 14:52:14,702 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:14,702 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'FAA', 'NOA'}
2025-07-03 14:52:14,702 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'FAA', 'BOA'}
2025-07-03 14:52:14,703 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:14,703 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:14,703 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'TAA'}
2025-07-03 14:52:14,703 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA'}
2025-07-03 14:52:14,703 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TRA', 'NOA', 'BeOA'}
2025-07-03 14:52:14,703 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:14,705 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:14,705 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:14,705 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'FAA', 'NOA'}
2025-07-03 14:52:14,705 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'BeOA'}
2025-07-03 14:52:14,706 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:14,706 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'FAA', 'BOA'}
2025-07-03 14:52:14,706 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'FAA', 'BeOA'}
2025-07-03 14:52:14,706 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:14,707 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:14,707 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'NOA'}
2025-07-03 14:52:14,707 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'TAA'}
2025-07-03 14:52:14,707 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA'}
2025-07-03 14:52:14,707 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:14,707 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:14,707 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:14,708 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:14,708 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:14,708 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:14,708 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:14,708 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TRA', 'TAA', 'BeOA'}
2025-07-03 14:52:14,709 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:14,709 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'FAA', 'BeOA'}
2025-07-03 14:52:14,709 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:14,709 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:14,709 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:14,710 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:14,719 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'BeOA'}
2025-07-03 14:52:14,719 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'NOA'}
2025-07-03 14:52:14,719 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'BeOA'}
2025-07-03 14:52:14,738 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:14,738 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:14,739 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:14,740 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:14,742 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:14,742 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:14,743 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:14,756 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'BeOA'}
2025-07-03 14:52:14,757 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TRA', 'BOA', 'BeOA'}
2025-07-03 14:52:15,409 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,416 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,417 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,418 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,420 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:15,423 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'NOA', 'BeOA'}
2025-07-03 14:52:15,424 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,426 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,426 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,426 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,427 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,428 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,428 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,429 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,430 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,433 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,434 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,435 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,438 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,438 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,438 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,439 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,440 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,441 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,443 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,443 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,454 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,454 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,455 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,455 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,464 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,469 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,473 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:15,480 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,480 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,481 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,482 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,483 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,484 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:15,490 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'NOA', 'BeOA'}
2025-07-03 14:52:15,499 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,506 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,507 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,508 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA'}
2025-07-03 14:52:15,509 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,510 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,511 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,511 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,512 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,513 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,514 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,515 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,525 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:15,526 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,528 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'NOA'}
2025-07-03 14:52:15,529 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,533 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,539 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,540 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,540 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,541 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,542 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,542 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,543 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,544 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,545 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,545 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,546 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,549 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,556 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:15,571 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,557 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,557 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,559 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,559 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:15,563 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,570 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,570 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,571 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,571 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,556 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,572 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,582 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,587 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,593 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,636 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,594 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,595 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,607 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,607 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,608 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,659 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,614 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,614 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,615 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,624 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,625 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,625 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,635 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,635 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,636 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:15,594 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,636 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,636 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,636 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,646 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,655 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,655 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'TAA'}
2025-07-03 14:52:15,665 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,665 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,666 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,614 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,676 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,682 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,738 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,694 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,694 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:15,694 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,695 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,695 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'NOA'}
2025-07-03 14:52:15,696 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,705 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,705 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,706 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,706 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,706 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,709 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,716 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,716 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'NOA'}
2025-07-03 14:52:15,717 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,717 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,720 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,727 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,777 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,728 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,737 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,737 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,737 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,738 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,777 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,688 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,738 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,777 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:15,742 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:15,748 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,777 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,758 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,777 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,758 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,758 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,758 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,762 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,778 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,768 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,771 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,777 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,778 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,777 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,777 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,777 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:15,777 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA'}
2025-07-03 14:52:15,777 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,778 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,778 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,777 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'TAA'}
2025-07-03 14:52:15,777 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,777 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,777 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:15,738 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'BeOA'}
2025-07-03 14:52:15,777 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,777 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,739 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,779 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,777 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,758 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:15,777 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:15,777 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,758 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:15,777 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,777 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:15,777 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,768 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,778 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,778 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,778 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,777 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,778 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA'}
2025-07-03 14:52:15,785 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,778 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'BeOA'}
2025-07-03 14:52:15,778 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,778 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,785 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,728 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,777 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'BeOA'}
2025-07-03 14:52:15,786 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,779 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'BOA'}
2025-07-03 14:52:15,786 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,779 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,779 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,779 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,779 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:15,779 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,779 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,777 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,786 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,779 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,779 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,779 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,779 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,779 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,786 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,784 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,785 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,785 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:15,786 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,785 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,786 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,785 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,778 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,785 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,785 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,778 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,786 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,792 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,786 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,779 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,786 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,786 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,786 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,786 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,786 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,792 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,786 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,779 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,786 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,786 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,786 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,793 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,786 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,786 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,786 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,784 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,786 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,786 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,791 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,799 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,785 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,791 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,799 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,791 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,800 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,792 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,792 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,779 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,792 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,792 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,792 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,792 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,792 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,792 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,786 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,792 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,792 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,792 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:15,793 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,793 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,786 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,797 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,798 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,798 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,798 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:15,799 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,799 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,799 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,799 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA'}
2025-07-03 14:52:15,785 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,808 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,799 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,791 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,800 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,791 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,800 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,806 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:15,806 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,806 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,806 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,806 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,806 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,806 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,806 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,806 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,806 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,806 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,818 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,807 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,818 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,807 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,807 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,807 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,808 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,808 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,808 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,818 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,808 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,808 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,800 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,808 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,808 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,808 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,808 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,824 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,809 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,809 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,809 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,825 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,809 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,810 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,830 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,812 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,830 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,817 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,818 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,818 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,806 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,807 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,818 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,818 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,818 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'NOA'}
2025-07-03 14:52:15,818 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,818 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,832 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,818 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,818 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,832 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,818 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,818 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,819 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,824 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:15,833 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,809 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,824 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,824 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,824 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'NOA', 'BeOA'}
2025-07-03 14:52:15,825 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,830 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,833 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,810 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,830 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,830 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,817 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,833 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,831 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,833 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,831 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,831 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,831 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,832 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,833 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,832 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,832 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,808 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,832 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,818 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,832 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,834 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,832 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,833 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,839 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,833 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:15,839 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,833 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,833 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,839 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,833 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,844 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,844 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,833 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,833 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,833 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,833 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:15,831 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,844 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,831 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,833 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,833 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,845 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,833 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,832 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,834 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,834 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,834 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,834 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,834 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,845 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,839 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,850 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,824 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,839 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,833 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,850 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,839 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,850 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,833 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,855 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,833 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:15,844 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,844 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,844 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,844 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,844 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,855 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,845 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,845 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,845 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,833 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,845 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,845 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,845 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,845 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,845 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,845 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,845 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,850 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,857 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,832 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,839 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,850 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,850 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,839 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,850 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,855 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,844 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,811 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,855 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,855 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,855 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,855 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,855 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,855 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,833 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,855 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,855 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,857 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,856 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:15,856 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,856 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,856 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,858 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,856 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,856 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,856 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,857 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,863 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,863 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,850 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:15,857 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,857 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,857 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,857 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,857 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,857 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,857 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,857 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,863 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,857 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,857 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,857 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,869 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,857 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,857 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,857 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,855 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,857 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,857 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,858 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,858 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,858 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,856 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,894 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,858 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,863 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,857 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:15,863 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,857 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,863 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,863 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,863 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,863 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,900 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,863 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,906 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,863 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'TAA'}
2025-07-03 14:52:15,863 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,857 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,911 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,869 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,869 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,874 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,857 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,874 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,874 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,879 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,884 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,922 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,889 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,889 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,894 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,895 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,899 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,863 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,899 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,899 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,899 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,899 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,900 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,900 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,900 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,863 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,906 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,863 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'NOA'}
2025-07-03 14:52:15,911 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,934 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,911 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,911 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:15,934 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,911 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,916 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,916 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,921 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,934 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,921 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,922 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,885 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,922 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:15,922 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,927 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,927 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,927 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,928 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,928 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,928 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,928 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,928 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,939 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,928 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,933 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,934 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,944 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,934 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,934 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,911 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,934 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA'}
2025-07-03 14:52:15,934 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,864 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,934 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,934 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'TAA'}
2025-07-03 14:52:15,950 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,934 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,934 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'BeOA'}
2025-07-03 14:52:15,950 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,939 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,939 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:15,939 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,939 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,939 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:15,939 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,939 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:15,939 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,939 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'NOA'}
2025-07-03 14:52:15,939 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:15,939 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,939 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,944 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:15,944 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,955 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,944 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,949 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,956 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,950 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:15,934 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,950 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,950 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,956 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,950 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,934 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'BOA'}
2025-07-03 14:52:15,956 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,950 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,950 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'BeOA'}
2025-07-03 14:52:15,922 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,950 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,950 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,950 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,956 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,950 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,950 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,950 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:15,950 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,950 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,950 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,956 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,955 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,955 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,955 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,929 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,956 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,956 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA'}
2025-07-03 14:52:15,956 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:15,957 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,950 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,956 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'BeOA'}
2025-07-03 14:52:15,957 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,956 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,956 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,956 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,956 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,958 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,956 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,956 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,956 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,956 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,956 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:15,958 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,950 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,956 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,958 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,956 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,956 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,956 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,950 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,963 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,956 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,956 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,956 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:15,956 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,957 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,965 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,956 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,956 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,957 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,950 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,957 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,970 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,957 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,957 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,950 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,958 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,958 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,958 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,958 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,958 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,956 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,958 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,970 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,963 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,963 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,970 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,963 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,963 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,956 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,963 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,970 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,964 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,964 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,964 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,965 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,957 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,965 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,965 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,965 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,969 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:15,957 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,970 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,970 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,970 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,971 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,970 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,970 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,970 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,970 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,970 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,970 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,977 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,970 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,977 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,970 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,970 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,970 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,979 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,970 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,963 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:15,970 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,970 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,985 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,970 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,970 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,970 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,970 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,971 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,971 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,971 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,971 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,971 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,971 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,987 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,971 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,970 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,976 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:15,976 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,976 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,977 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,988 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,977 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,978 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,958 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,978 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:15,979 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,979 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,984 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:15,988 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,984 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,985 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,988 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,985 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,988 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,985 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,985 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,988 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,985 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,986 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,986 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:15,986 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,987 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,987 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,971 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,987 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,988 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,988 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,988 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,988 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,956 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,988 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,994 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,988 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,988 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,995 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,988 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,970 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:15,988 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,988 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'NOA'}
2025-07-03 14:52:15,988 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,970 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,988 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,985 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,988 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,985 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:15,988 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,988 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,993 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:15,994 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,994 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,994 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,994 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,998 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,994 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,994 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,999 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,994 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'NOA', 'BeOA'}
2025-07-03 14:52:15,994 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,994 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,994 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,994 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,988 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,995 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,996 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,988 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,996 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,996 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,996 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,996 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:15,996 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:15,996 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,996 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,997 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,997 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,011 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:15,998 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:15,998 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,011 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:15,998 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,998 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:15,998 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:15,998 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:15,994 - __main__ - INFO - ============================================================
2025-07-03 14:52:15,999 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,004 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:15,994 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,004 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,009 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,009 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,010 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,010 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,010 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,010 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,010 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,010 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,010 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,013 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,011 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:16,011 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,011 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,011 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,011 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,011 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,011 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:15,997 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,011 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,011 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,011 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA'}
2025-07-03 14:52:15,998 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,011 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,011 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,011 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,012 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:16,012 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,012 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,012 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,012 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,012 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,023 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:16,012 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,012 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,012 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,013 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,013 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,013 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,013 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,011 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,013 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,013 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,013 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,013 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,018 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,018 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,022 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,024 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,023 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,023 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,023 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,023 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,023 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,023 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,025 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,023 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,023 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,023 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:16,023 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,023 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,012 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,023 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,023 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,023 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,023 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,023 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,023 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,023 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,036 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,023 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,024 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,024 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,037 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,024 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,024 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,037 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:16,023 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,024 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:16,025 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,025 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,025 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,025 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,025 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,023 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,025 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,025 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,030 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,030 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,030 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,030 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,030 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,030 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,031 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:16,036 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,048 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,036 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,036 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,023 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,037 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:16,037 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,037 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,024 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:16,037 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,054 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,037 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,037 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,059 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,042 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,042 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,043 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,043 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,043 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,043 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:16,043 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,048 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,048 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,048 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,048 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,048 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,048 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,048 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,048 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,048 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,049 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'BOA'}
2025-07-03 14:52:16,036 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,049 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,049 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,053 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,053 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,053 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,054 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,054 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,054 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,024 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,058 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,042 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,059 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,059 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,059 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,085 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,059 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,059 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,059 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,064 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,064 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,064 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,064 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA'}
2025-07-03 14:52:16,101 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,069 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,069 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TRA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,074 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,074 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,074 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,074 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,075 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,102 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,079 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,079 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,079 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,085 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,085 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,107 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,085 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,085 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,085 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,113 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,113 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,085 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,090 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,059 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,096 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,101 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,101 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,101 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,101 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'TAA'}
2025-07-03 14:52:16,113 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,064 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,102 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,113 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,102 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'FAA', 'TRA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (1.40s)
2025-07-03 14:52:16,102 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,113 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'BeOA'}
2025-07-03 14:52:16,102 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,113 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,113 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,107 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,079 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,107 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,107 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,107 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,085 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,112 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,119 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,112 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,085 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,113 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,113 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:16,085 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,113 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,113 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,113 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,120 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:16,113 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,120 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'NOA'}
2025-07-03 14:52:16,101 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,113 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,113 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,102 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,113 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,113 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,121 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,102 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,113 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,113 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,107 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,113 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,119 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,119 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,119 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,119 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:16,119 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,112 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,119 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'NOA'}
2025-07-03 14:52:16,119 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,119 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,120 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,120 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,120 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,120 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:16,120 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,120 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,113 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,113 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'BeOA'}
2025-07-03 14:52:16,120 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,120 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,120 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,121 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,121 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:16,147 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,102 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,121 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,121 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,121 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,121 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,121 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'BeOA'}
2025-07-03 14:52:16,121 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,121 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,121 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,129 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,134 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,134 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,148 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,134 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,139 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,139 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,140 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,140 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,140 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,140 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,140 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,140 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,140 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,140 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,146 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,155 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,146 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,147 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,121 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,147 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,147 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,156 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,148 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,148 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,148 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,148 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,158 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,148 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,148 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,164 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,153 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,139 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,154 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,154 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,154 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,154 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,154 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,174 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,155 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,174 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,155 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,174 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,155 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,155 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'TAA'}
2025-07-03 14:52:16,146 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,155 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,155 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,156 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,156 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,157 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,148 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,158 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,148 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,158 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,186 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,158 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,164 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,148 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,164 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,164 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,186 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'NOA'}
2025-07-03 14:52:16,164 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,173 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:16,186 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,174 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,174 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,174 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,155 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,174 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,155 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,179 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,179 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,155 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,180 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,180 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,186 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,180 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,180 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,180 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,180 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,186 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,186 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,186 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,158 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,186 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,186 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,187 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,186 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,186 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,167 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,186 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,186 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA'}
2025-07-03 14:52:16,173 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,186 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,186 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,186 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,186 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,186 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,186 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,186 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,194 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,186 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,186 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,186 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,187 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,180 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,187 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,195 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,187 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,187 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,187 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,187 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,187 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,187 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,187 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,186 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,197 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:16,192 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,193 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,197 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,193 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,193 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,197 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,193 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,193 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,194 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'BOA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,194 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,197 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,194 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,186 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,194 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'BeOA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,195 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,202 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,195 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,195 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,195 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,195 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,195 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,187 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,195 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,195 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,195 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,196 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,203 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,197 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,187 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,203 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,197 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,193 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,197 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,197 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,193 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,197 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,197 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,197 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,197 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'BOA', 'TAA'} 模拟完成: 0.0000 (1.49s)
2025-07-03 14:52:16,202 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,204 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,202 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,202 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:16,202 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'BeOA', 'TAA'} 模拟完成: 0.0000 (1.50s)
2025-07-03 14:52:16,195 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,202 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,202 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,202 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,204 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,203 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,209 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,203 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,203 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,203 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,203 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,196 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,203 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,203 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,197 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,210 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,203 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:16,203 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,203 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,203 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,203 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,204 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,210 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,204 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,204 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,194 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,204 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:16,204 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,204 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,204 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'BeOA', 'NOA'}
2025-07-03 14:52:16,204 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,204 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,209 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,209 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,209 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,203 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,216 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,209 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,210 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,210 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,210 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,210 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,216 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,210 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,203 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,210 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,210 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,210 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,217 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,210 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,210 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,211 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,204 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,211 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,217 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,211 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,211 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,216 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,216 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,222 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,216 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,216 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,216 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,216 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,216 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,216 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,203 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,216 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,216 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,216 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,216 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,217 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,217 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,210 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,217 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,217 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,217 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,258 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,210 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,217 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,217 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,217 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,217 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,222 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,259 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,211 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,222 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,222 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,216 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'BeOA', 'NOA'}
2025-07-03 14:52:16,223 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,223 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,224 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:16,224 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,231 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,240 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,241 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,260 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,251 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,251 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,257 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,257 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,257 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,258 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,258 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,258 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,217 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,258 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,258 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,258 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,258 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,258 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,259 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:16,222 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,259 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,259 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,291 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,259 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,260 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,260 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,260 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,260 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,260 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,297 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,246 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,267 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,267 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,303 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'BOA'}
2025-07-03 14:52:16,278 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,278 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,278 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,278 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,308 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,279 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,279 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,279 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,279 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'NOA'} 模拟完成: 0.0000 (1.58s)
2025-07-03 14:52:16,285 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,290 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,290 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,290 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,310 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,290 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,291 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,259 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA'}
2025-07-03 14:52:16,291 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,292 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,297 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,297 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,297 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,302 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,303 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,260 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,303 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,303 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,278 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,303 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'BeOA'}
2025-07-03 14:52:16,303 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,308 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,308 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,278 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,308 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,308 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,309 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,309 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA'}
2025-07-03 14:52:16,309 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,309 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,309 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,310 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,290 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,310 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,310 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,310 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,310 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,336 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,310 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,310 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,311 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,311 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,311 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA'}
2025-07-03 14:52:16,311 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'TAA'}
2025-07-03 14:52:16,311 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:16,336 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,320 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:16,336 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,321 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,321 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,321 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,326 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,326 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,326 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,326 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,326 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,341 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,326 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA'}
2025-07-03 14:52:16,330 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:16,342 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,336 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,336 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,336 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,336 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,336 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,336 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,336 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,310 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,336 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'NOA'}
2025-07-03 14:52:16,336 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,336 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,336 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA'}
2025-07-03 14:52:16,336 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,336 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,365 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,314 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,321 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,336 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,365 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,336 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,336 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,336 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,341 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,341 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,341 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,341 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,327 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:16,336 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:16,342 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,342 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,342 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,376 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,342 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,347 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,347 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,347 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,348 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,354 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,359 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,365 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,365 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,382 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,336 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,365 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,365 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,365 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,336 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,365 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,365 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,365 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,365 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'BeOA'}
2025-07-03 14:52:16,365 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,365 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,370 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,371 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,371 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,371 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,371 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,376 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,342 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,381 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:16,381 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 0.0000 (1.68s)
2025-07-03 14:52:16,382 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,390 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:16,382 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,390 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,382 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,382 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,382 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,396 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'BeOA'}
2025-07-03 14:52:16,365 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,383 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,383 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,396 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,383 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,383 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,383 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,388 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,397 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,389 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,389 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,389 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,389 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,389 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,400 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,389 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA', 'NOA'}
2025-07-03 14:52:16,389 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,389 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,390 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,390 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,390 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,382 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,411 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,382 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,412 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,396 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,396 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 0.0000 (1.70s)
2025-07-03 14:52:16,396 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,383 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,396 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,417 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,397 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'FAA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,397 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,397 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,417 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,383 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,397 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,389 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,417 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,397 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,417 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,400 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,405 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,422 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,411 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:16,422 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,411 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,411 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA'}
2025-07-03 14:52:16,411 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,411 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,411 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,390 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,412 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,416 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,416 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,396 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,417 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:16,417 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,417 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,396 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,417 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,417 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'FAA', 'TAA'} 模拟完成: 0.0000 (1.72s)
2025-07-03 14:52:16,417 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'FAA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,417 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,397 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,417 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,417 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,436 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,397 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,417 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,400 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA', 'NOA'}
2025-07-03 14:52:16,422 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,389 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,422 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'}
2025-07-03 14:52:16,422 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA'}
2025-07-03 14:52:16,411 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,422 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,422 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,422 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,422 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,422 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,422 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'BOA'} 模拟完成: 0.0000 (1.72s)
2025-07-03 14:52:16,422 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,423 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'TRA', 'BeOA', 'TAA'}
2025-07-03 14:52:16,423 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,437 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,423 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,423 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,423 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,424 - __main__ - INFO - 联盟 {'FAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,430 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'BeOA', 'BOA'}
2025-07-03 14:52:16,435 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'FAA', 'BOA'} 模拟完成: 0.0000 (1.73s)
2025-07-03 14:52:16,435 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'FAA', 'BeOA'} 模拟完成: 0.0000 (1.73s)
2025-07-03 14:52:16,436 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:16,436 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,436 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,436 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,417 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,436 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,436 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,438 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,436 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,436 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,438 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,436 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,436 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,438 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,436 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,437 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,437 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,437 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,439 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,437 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'TAA'}
2025-07-03 14:52:16,423 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:16,437 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,437 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,437 - __main__ - INFO - 联盟 {'TRA', 'BOA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,437 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'BOA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,437 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA'} 模拟完成: 0.0000 (1.73s)
2025-07-03 14:52:16,437 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'BeOA', 'BOA'}
2025-07-03 14:52:16,437 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'BeOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,437 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'BeOA', 'BOA', 'TAA'}
2025-07-03 14:52:16,437 - __main__ - INFO - 📊 并发进度: 10/56 (17.9%)
2025-07-03 14:52:16,437 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,463 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,437 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,437 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,437 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,437 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,438 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,436 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,438 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,438 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,436 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,438 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,438 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:16,437 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (1.74s)
2025-07-03 14:52:16,438 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,438 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,438 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,437 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,439 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,439 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'NOA'} 模拟完成: 0.0000 (1.73s)
2025-07-03 14:52:16,439 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,439 - __main__ - DEBUG - ✅ 联盟 {'TRA', 'BOA', 'TAA'} 模拟完成: 0.0000 (1.73s)
2025-07-03 14:52:16,439 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'BOA', 'TAA'} 模拟完成: 0.0000 (1.74s)
2025-07-03 14:52:16,445 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'FAA'}
2025-07-03 14:52:16,478 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'FAA'}
2025-07-03 14:52:16,462 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'BeOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,463 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,463 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,437 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,464 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,469 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'BOA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,475 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,475 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,485 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,476 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,476 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,476 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'TAA'} 模拟完成: 0.0000 (1.77s)
2025-07-03 14:52:16,476 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,476 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,476 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'BeOA'}
2025-07-03 14:52:16,477 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,477 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,477 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,477 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,477 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,477 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,478 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TRA', 'TAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,478 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'BOA'}
2025-07-03 14:52:16,478 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,462 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'BeOA', 'BOA', 'TAA'}
2025-07-03 14:52:16,478 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,484 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,484 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,484 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,484 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'BOA', 'TAA', 'NOA'} 模拟完成: 0.0000 (1.78s)
2025-07-03 14:52:16,484 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (1.78s)
2025-07-03 14:52:16,475 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:16,485 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'FAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,485 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'BeOA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,497 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,498 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,498 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,498 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,498 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'BeOA'}
2025-07-03 14:52:16,498 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,499 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,499 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,499 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,499 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,500 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,500 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'FAA', 'TRA', 'TAA', 'BeOA'} 模拟完成: 0.0000 (1.79s)
2025-07-03 14:52:16,500 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'BOA'}
2025-07-03 14:52:16,501 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,507 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,507 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,508 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,512 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'BeOA'} 模拟完成: 0.0000 (1.79s)
2025-07-03 14:52:16,513 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'BeOA', 'BOA'}
2025-07-03 14:52:16,513 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'BeOA', 'NOA'}
2025-07-03 14:52:16,513 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,535 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,516 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'BeOA', 'TAA'} 模拟完成: 0.0000 (1.81s)
2025-07-03 14:52:16,527 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,527 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,528 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:16,529 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,530 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,531 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:16,531 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'TAA', 'NOA'} 模拟完成: 0.0000 (1.82s)
2025-07-03 14:52:16,531 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,532 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,532 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:16,533 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,534 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,534 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (1.83s)
2025-07-03 14:52:16,535 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,535 - __main__ - INFO - 📊 并发进度: 20/56 (35.7%)
2025-07-03 14:52:16,535 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'BeOA', 'BOA'}
2025-07-03 14:52:16,535 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'BeOA', 'NOA'}
2025-07-03 14:52:16,514 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'FAA', 'NOA'} 模拟完成: 0.0000 (1.81s)
2025-07-03 14:52:16,536 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,536 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA'}
2025-07-03 14:52:16,538 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'BeOA'} 模拟完成: 0.0000 (1.83s)
2025-07-03 14:52:16,539 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,539 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,546 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (1.83s)
2025-07-03 14:52:16,546 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,546 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'TAA'}
2025-07-03 14:52:16,553 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,553 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,564 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'FAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:16,570 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:16,570 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,571 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:16,572 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,574 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,586 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,586 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA'}
2025-07-03 14:52:16,587 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'NAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:16,595 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,596 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,601 - __main__ - DEBUG - 🔄 开始并发模拟联盟: {'FAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:16,602 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,614 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'TAA'}
2025-07-03 14:52:16,615 - __main__ - INFO - 联盟 {'TRA', 'BeOA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,615 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,617 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,617 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,618 - __main__ - INFO - 开始联盟交易模拟: {'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:16,618 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,619 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,620 - __main__ - INFO - 开始联盟交易模拟: {'NAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:16,621 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,622 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,622 - __main__ - INFO - 开始联盟交易模拟: {'FAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:16,634 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,635 - __main__ - DEBUG - ✅ 联盟 {'TRA', 'BeOA', 'TAA'} 模拟完成: 0.0000 (1.93s)
2025-07-03 14:52:16,636 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'TAA'} 模拟完成: 0.0000 (1.93s)
2025-07-03 14:52:16,637 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,639 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,656 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,662 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,665 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:16,669 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:16,671 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,672 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,673 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,675 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'NOA'} 模拟完成: 0.0000 (1.97s)
2025-07-03 14:52:16,675 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,688 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,690 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,692 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,692 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,706 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,715 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,720 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,721 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA', 'NOA'}
2025-07-03 14:52:16,725 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,736 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,736 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,754 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:16,755 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,755 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,761 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,762 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,762 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (2.05s)
2025-07-03 14:52:16,763 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,765 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,766 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,766 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,766 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,766 - __main__ - INFO - 📊 并发进度: 30/56 (53.6%)
2025-07-03 14:52:16,779 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'TRA', 'TAA'}
2025-07-03 14:52:16,780 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,781 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,782 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,783 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:16,784 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,784 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,784 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,791 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,809 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,810 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,811 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:16,816 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,813 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,815 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,815 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,816 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,813 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,818 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:16,819 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,820 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,822 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,823 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,824 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,825 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,826 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,826 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,828 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,830 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,830 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,831 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:16,831 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,832 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,833 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,833 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,835 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,835 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,836 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,840 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,845 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,846 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,853 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:16,860 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:16,860 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,861 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,861 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,862 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,862 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,864 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,862 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,863 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,863 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:16,864 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,864 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,862 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,865 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,871 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,871 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,871 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,871 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,873 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,874 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,875 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,877 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,878 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,878 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,879 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,879 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,880 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA', 'NOA'}
2025-07-03 14:52:16,880 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,886 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:16,888 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,889 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,889 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,892 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,892 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,893 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,895 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:16,896 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,902 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,903 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,904 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,904 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'TRA', 'TAA'}
2025-07-03 14:52:16,904 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,905 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,907 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,917 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:16,919 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,921 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,930 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:16,930 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,931 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,931 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,932 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,938 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,934 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,938 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,935 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,940 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:16,936 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,937 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:16,938 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,939 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,934 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,933 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,935 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,945 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,940 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,941 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,942 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,943 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,944 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,944 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA', 'BOA'}
2025-07-03 14:52:16,950 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,945 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,946 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,948 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,949 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:16,949 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,949 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,950 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:16,941 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,951 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,951 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,952 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,952 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,953 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:16,953 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,954 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:16,955 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,956 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:16,957 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,957 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,958 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:16,958 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:16,959 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,959 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,960 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,961 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,963 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:16,964 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,964 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'BOA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (0.85s)
2025-07-03 14:52:16,965 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:16,965 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,968 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:16,974 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:16,977 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:16,983 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:16,984 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,984 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,985 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:16,992 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:16,993 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:16,993 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,008 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:16,995 - __main__ - INFO - ============================================================
2025-07-03 14:52:16,996 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,001 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,007 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,008 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,008 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,008 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:16,994 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,009 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA'}
2025-07-03 14:52:17,009 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,010 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,010 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,011 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,012 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,012 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,013 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,013 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,014 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,015 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,016 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA', 'NOA'}
2025-07-03 14:52:17,016 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,017 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,017 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,018 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,018 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,019 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,019 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,020 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,020 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,022 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,022 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,023 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,024 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,024 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,024 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,025 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,025 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,026 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,026 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,032 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,037 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,037 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,041 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,047 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,048 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,048 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,053 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,063 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,064 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,066 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,066 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,066 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,067 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,067 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,071 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,068 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,069 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,069 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,069 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,075 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,070 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:17,071 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'TRA', 'TAA'}
2025-07-03 14:52:17,072 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA'}
2025-07-03 14:52:17,068 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,073 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,078 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,074 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA', 'BOA'}
2025-07-03 14:52:17,074 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'BeOA', 'BOA', 'TAA'}
2025-07-03 14:52:17,070 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,075 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,075 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,076 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,077 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,078 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,078 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,079 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,079 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,073 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,079 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,080 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,080 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,105 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,081 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,082 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'BeOA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,082 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,083 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,084 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,093 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,093 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'FAA', 'TRA', 'BOA', 'TAA', 'NOA'} 模拟完成: 0.0000 (0.89s)
2025-07-03 14:52:17,093 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,104 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,080 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,105 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,094 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,105 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,105 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'BeOA', 'NOA'} 模拟完成: 0.0000 (0.90s)
2025-07-03 14:52:17,105 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,106 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,107 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,107 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,108 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,108 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,108 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,109 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,109 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,116 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,123 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,123 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,129 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,129 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,149 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,130 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,134 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,140 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,140 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'BeOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,140 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,141 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,147 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,148 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,149 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,149 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,149 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,150 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,130 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,151 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,151 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,151 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,152 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,155 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,162 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,162 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,162 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,162 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,163 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,172 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,172 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA', 'BOA'}
2025-07-03 14:52:17,178 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,179 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,180 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,183 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,188 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,188 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,188 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,188 - __main__ - INFO - 联盟 {'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,189 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,189 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,195 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,195 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,196 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,199 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,205 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,205 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,206 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,206 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,206 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,215 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,219 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,263 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,263 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,228 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,234 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,234 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,234 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,244 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,251 - __main__ - DEBUG - ✅ 联盟 {'TRA', 'TAA'} 模拟完成: 0.0000 (0.94s)
2025-07-03 14:52:17,256 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,264 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,263 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA'}
2025-07-03 14:52:17,263 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,263 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,263 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,263 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,263 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,263 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,263 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,263 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,264 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,263 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,263 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,264 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:17,264 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,264 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,234 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,264 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA'}
2025-07-03 14:52:17,264 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,264 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,264 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,265 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,262 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,264 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,265 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,264 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,264 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,264 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,264 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,264 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:17,264 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,264 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,263 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,266 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,264 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,225 - __main__ - INFO - 触发第 1 周Shapley值计算
2025-07-03 14:52:17,264 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,265 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,265 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,266 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:17,265 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,265 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,264 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA'}
2025-07-03 14:52:17,265 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,266 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,265 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,264 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:17,267 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,265 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,265 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,265 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,265 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,266 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,266 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,264 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,266 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,266 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,267 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,266 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,266 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,265 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,266 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,266 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,266 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,266 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,267 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,265 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,267 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA', 'NOA'}
2025-07-03 14:52:17,267 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,265 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,267 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,267 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,267 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,279 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,267 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,267 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,266 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,268 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,268 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,268 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,268 - __main__ - INFO - 第 1 周性能分析 - 联盟: {'NAA', 'TRA', 'TAA'}
2025-07-03 14:52:17,268 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,268 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,268 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,269 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,290 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,269 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,269 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,269 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,269 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,269 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,269 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,292 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,279 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,279 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,267 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,293 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,279 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,279 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,280 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,280 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,285 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,295 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,285 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,290 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,290 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,290 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,290 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,291 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,295 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,269 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,292 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,295 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,292 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,293 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,293 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,293 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,279 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,293 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,293 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,293 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,294 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,295 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,295 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA', 'BOA'}
2025-07-03 14:52:17,290 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,295 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,297 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,295 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,295 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,295 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,292 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,297 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,295 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,274 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,295 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,296 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,296 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,296 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA'}
2025-07-03 14:52:17,296 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,296 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,299 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,297 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,297 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,297 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,299 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,297 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,297 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,299 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'TRA', 'BeOA', 'BOA', 'TAA'}
2025-07-03 14:52:17,297 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,297 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,297 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,297 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,295 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,297 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,301 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,301 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,298 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,298 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,298 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,298 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,299 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,299 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,301 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,297 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,299 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,299 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,297 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,299 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,311 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,295 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,300 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,300 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,312 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,300 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,300 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,298 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,298 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,301 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,301 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,301 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,301 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,318 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,301 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,306 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,299 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (0.91s)
2025-07-03 14:52:17,306 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,311 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,311 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,311 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,312 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,299 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,312 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,312 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,312 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,317 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,300 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,317 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,324 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,318 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:17,318 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,318 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,318 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,318 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,301 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,329 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,323 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,323 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,323 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,323 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,324 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,324 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,324 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,324 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,324 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,330 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,324 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,324 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,318 - __main__ - INFO - 第 6 天开始周期性重新评估
2025-07-03 14:52:17,336 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,336 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,324 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,329 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,329 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,329 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,330 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,323 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,330 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA', 'BOA'}
2025-07-03 14:52:17,330 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,330 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,330 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,330 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,347 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,330 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,330 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,324 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,330 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,330 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'TRA', 'BeOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,336 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,324 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,336 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,336 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,353 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,336 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,336 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,341 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,341 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,341 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,341 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,341 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,341 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,347 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,330 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,347 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,347 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,347 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,347 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,347 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,347 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,359 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,353 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,359 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,353 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,353 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,359 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,353 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,353 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,360 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:17,354 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,354 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,354 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,354 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,354 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,360 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,359 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,359 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,359 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,361 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,359 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,348 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,359 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,359 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,329 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,359 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,360 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,353 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,360 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,354 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,377 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,360 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,360 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,360 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,360 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,359 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,360 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,361 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,361 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,359 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,361 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,366 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,388 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,367 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,367 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,367 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,367 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,367 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,372 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,377 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,393 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,360 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,377 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,377 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,382 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,382 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,382 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,382 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,382 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,382 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,382 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,388 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,399 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,388 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'TAA'}
2025-07-03 14:52:17,388 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,388 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,400 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,388 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA'}
2025-07-03 14:52:17,400 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,393 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,393 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA', 'NOA'}
2025-07-03 14:52:17,394 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,394 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,377 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,394 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,394 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,400 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:17,394 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,394 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,394 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,394 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,399 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,366 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,399 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:17,399 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,400 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,400 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,400 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,393 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,400 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,393 - __main__ - INFO - 触发第 2 周Shapley值计算
2025-07-03 14:52:17,402 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,400 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,400 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,402 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,400 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,394 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:17,401 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,402 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,401 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,402 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,401 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,401 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,401 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,401 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,401 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,401 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,401 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,401 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,402 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,402 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,402 - __main__ - INFO - 第 2 周性能分析 - 联盟: {'NAA', 'TRA'}
2025-07-03 14:52:17,400 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,403 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,402 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,400 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,404 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,402 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,402 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,401 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,402 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,404 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,402 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,403 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA'}
2025-07-03 14:52:17,403 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,403 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,403 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,403 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,403 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,411 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,403 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,411 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,403 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,402 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,403 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,403 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,404 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,402 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,404 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,404 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,404 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,404 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,410 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,413 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,410 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,410 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,410 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,410 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,410 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,411 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,413 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,411 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,413 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,411 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,411 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,412 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,412 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,412 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,412 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,412 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,412 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,413 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,419 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,413 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,419 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,413 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,413 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,425 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,413 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,413 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,403 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,426 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,403 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,426 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,413 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,413 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,413 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,431 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,414 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,414 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,418 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,432 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,419 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,432 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,413 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'BeOA', 'BOA'}
2025-07-03 14:52:17,432 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,425 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,432 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,432 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,413 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,426 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,433 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,426 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,431 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,438 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,431 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,431 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,413 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,432 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,432 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,432 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,432 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA'}
2025-07-03 14:52:17,432 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,419 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,432 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,424 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,432 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,426 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,425 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,439 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,432 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,413 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,438 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,438 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,438 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:17,413 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,438 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,438 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,438 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,439 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,440 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,439 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,439 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,439 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,439 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,439 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,439 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,446 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,439 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,401 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,439 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,440 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,446 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,440 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,440 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,440 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,440 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,440 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,440 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,440 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,440 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,439 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,445 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,446 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,446 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,446 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,446 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,446 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,452 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,446 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,446 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,446 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,446 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,440 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,446 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'TRA', 'BeOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,453 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,446 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,446 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,446 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,446 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,446 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,452 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,452 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,452 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,452 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,452 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,452 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,439 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,452 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,453 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,453 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,453 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,453 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,453 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,453 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,446 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,453 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,470 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,453 - __main__ - INFO - 第 11 天开始周期性重新评估
2025-07-03 14:52:17,458 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,459 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,464 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,464 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,464 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,464 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA', 'BOA'}
2025-07-03 14:52:17,464 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,465 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,465 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,465 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,465 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'BeOA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,465 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,480 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,465 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,465 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,465 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,470 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,470 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,453 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (1.02s)
2025-07-03 14:52:17,470 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,470 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,470 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,481 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA'}
2025-07-03 14:52:17,470 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,471 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:17,471 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,471 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,471 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,475 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,482 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,480 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,480 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'BeOA', 'BOA'} 模拟完成: 0.0000 (1.05s)
2025-07-03 14:52:17,481 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,465 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,481 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,487 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,481 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,481 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,481 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,481 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,481 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,470 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,481 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,481 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,481 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,493 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,482 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,482 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,480 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,482 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:17,493 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,482 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,487 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,494 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,492 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,492 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,492 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,492 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,493 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,499 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,493 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,493 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,499 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,493 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,482 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,493 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,499 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,493 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,493 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,499 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,494 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,499 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,481 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,494 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,499 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,499 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,499 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'BOA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,493 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,499 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,499 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,499 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,514 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,493 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,499 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,499 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,493 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,499 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,515 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,482 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,520 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,509 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,520 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,494 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'TRA', 'BeOA', 'BOA', 'TAA'}
2025-07-03 14:52:17,509 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,509 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,509 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'BOA', 'NOA'} 模拟完成: 0.0000 (1.09s)
2025-07-03 14:52:17,509 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,509 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,514 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,499 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,515 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,515 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,515 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,515 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,515 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,520 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,520 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,499 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,520 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,520 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,509 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'TAA'}
2025-07-03 14:52:17,520 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,521 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,520 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,520 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,521 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,520 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,520 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,520 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,520 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,520 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,520 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,504 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,520 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,520 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,520 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,521 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,521 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,521 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,529 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,521 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,521 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,529 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,526 - __main__ - INFO - 触发第 3 周Shapley值计算
2025-07-03 14:52:17,529 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,526 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,526 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,527 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,527 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,530 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,527 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,528 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,528 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:17,528 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,535 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,529 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,529 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,529 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,520 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:17,529 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,520 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,529 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA'}
2025-07-03 14:52:17,526 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,529 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,529 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,530 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,530 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,536 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,534 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,534 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,535 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,529 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,535 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,535 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,535 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,536 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,535 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,535 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,535 - __main__ - INFO - 第 3 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA', 'NOA'}
2025-07-03 14:52:17,537 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,535 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,535 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,535 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,536 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,528 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,536 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,536 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,536 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,536 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,538 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,536 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,536 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,536 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,537 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,535 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,537 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,537 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,535 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,537 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,548 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,537 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,537 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,537 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'FAA'}
2025-07-03 14:52:17,549 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,537 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,538 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,538 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,543 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,536 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,543 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,543 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,543 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,543 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,543 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,543 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,548 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,537 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,548 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,548 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,549 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,537 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA'}
2025-07-03 14:52:17,549 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,549 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,549 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,549 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,549 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,549 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,549 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,550 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,550 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,550 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,550 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,550 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,550 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,550 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,550 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,555 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,555 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,555 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,555 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,560 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,560 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,561 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,562 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,561 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,562 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,561 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,561 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,561 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,561 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,568 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,561 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'TRA', 'BeOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,561 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,562 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,562 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,562 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,562 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,562 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,562 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,580 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA', 'BOA'}
2025-07-03 14:52:17,562 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,561 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,580 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,568 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,568 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,580 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,568 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,561 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,574 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,581 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,574 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,574 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,574 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA'}
2025-07-03 14:52:17,582 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,580 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,580 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,562 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,580 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,580 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,580 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,561 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,582 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,568 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,580 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,581 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,581 - __main__ - INFO - 第 16 天开始周期性重新评估
2025-07-03 14:52:17,582 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,581 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'NOA'}
2025-07-03 14:52:17,582 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,582 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,580 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,582 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,582 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,582 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'FAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,582 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,583 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,582 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,582 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,580 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,583 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'FAA', 'TRA', 'BOA', 'NOA'}
2025-07-03 14:52:17,582 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,582 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,583 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,582 - __main__ - INFO - 使用模拟数据刷新分析缓存...
2025-07-03 14:52:17,589 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,582 - __main__ - INFO - 分析缓存已清空
2025-07-03 14:52:17,582 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,582 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,582 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,582 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,582 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,582 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'FAA'} 模拟完成: 0.0000 (1.14s)
2025-07-03 14:52:17,582 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,583 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,583 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,600 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,583 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,583 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,583 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,589 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,574 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,589 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,582 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,589 - __main__ - DEBUG - 已缓存智能体 NAA 的分析结果
2025-07-03 14:52:17,594 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,594 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,611 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,594 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,594 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,595 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,600 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,605 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,582 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,605 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,605 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,611 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,611 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,611 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,612 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,611 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,611 - __main__ - DEBUG - 已缓存智能体 TAA 的分析结果
2025-07-03 14:52:17,611 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,594 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:17,612 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,611 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,611 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'BOA'} 模拟完成: 0.0000 (1.13s)
2025-07-03 14:52:17,611 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,611 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,611 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,611 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,611 - __main__ - INFO - 联盟 {'TRA', 'BOA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,611 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,612 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,612 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,612 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,611 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,612 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,612 - __main__ - DEBUG - 已缓存智能体 FAA 的分析结果
2025-07-03 14:52:17,623 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,612 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,611 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,617 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,629 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,617 - __main__ - INFO - 📊 并发进度: 40/56 (71.4%)
2025-07-03 14:52:17,617 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,617 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:17,629 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,623 - __main__ - DEBUG - ✅ 联盟 {'TRA', 'BOA', 'TAA', 'NOA'} 模拟完成: 0.0000 (1.14s)
2025-07-03 14:52:17,623 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,623 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'TRA', 'BeOA', 'BOA', 'TAA'}
2025-07-03 14:52:17,623 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,623 - __main__ - INFO - 模拟数据缓存刷新完成
2025-07-03 14:52:17,623 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,623 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,628 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,634 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,612 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,629 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,629 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA'}
2025-07-03 14:52:17,634 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,629 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'BeOA', 'BOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,629 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,623 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,629 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,629 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,629 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,634 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,634 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,634 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,634 - __main__ - INFO - 联盟 {'TRA', 'BeOA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,628 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,635 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,634 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'BeOA'} 模拟完成: 0.0000 (1.16s)
2025-07-03 14:52:17,617 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'}
2025-07-03 14:52:17,635 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,634 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,634 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,634 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,634 - __main__ - INFO - 联盟 {'FAA', 'TRA', 'BOA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,634 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,635 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,634 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,635 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'BOA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,635 - __main__ - DEBUG - ✅ 联盟 {'TRA', 'BeOA', 'TAA', 'NOA'} 模拟完成: 0.0000 (1.20s)
2025-07-03 14:52:17,634 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'BeOA', 'NOA'}
2025-07-03 14:52:17,635 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,634 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,634 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'BeOA', 'BOA'} 模拟完成: 0.0000 (1.12s)
2025-07-03 14:52:17,635 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,635 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,635 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,635 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'TRA', 'TAA'}
2025-07-03 14:52:17,635 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,635 - __main__ - DEBUG - ✅ 联盟 {'FAA', 'TRA', 'BOA', 'NOA'} 模拟完成: 0.0000 (1.03s)
2025-07-03 14:52:17,635 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,635 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,635 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'BOA', 'TAA', 'NOA'} 模拟完成: 0.0000 (1.06s)
2025-07-03 14:52:17,635 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,636 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,635 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,635 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,635 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,636 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,636 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,634 - __main__ - INFO - 触发第 4 周Shapley值计算
2025-07-03 14:52:17,636 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,636 - __main__ - INFO - 联盟 {'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,636 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,636 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,635 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,636 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,636 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,635 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,636 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,636 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,636 - __main__ - INFO - 联盟 {'TRA', 'BeOA', 'BOA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,636 - __main__ - DEBUG - ✅ 联盟 {'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (1.14s)
2025-07-03 14:52:17,636 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,637 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,636 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'BOA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,636 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,636 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'TAA', 'NOA'} 模拟完成: 0.0000 (1.05s)
2025-07-03 14:52:17,636 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,636 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,637 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,637 - __main__ - DEBUG - ✅ 联盟 {'TRA', 'BeOA', 'BOA', 'TAA'} 模拟完成: 0.0000 (1.20s)
2025-07-03 14:52:17,637 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'BeOA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,636 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,637 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'BOA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (1.16s)
2025-07-03 14:52:17,637 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'BOA', 'TAA', 'NOA', 'BeOA'} 模拟完成: 0.0000 (1.10s)
2025-07-03 14:52:17,637 - __main__ - INFO - 联盟 {'NAA', 'TRA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,636 - __main__ - INFO - 第 4 周性能分析 - 联盟: {'NAA', 'FAA', 'TRA', 'TAA', 'NOA'}
2025-07-03 14:52:17,637 - __main__ - INFO - 周总收益率: 0.0000
2025-07-03 14:52:17,637 - __main__ - INFO - 联盟 {'NAA', 'TRA', 'TAA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,637 - __main__ - INFO - 联盟 {'TRA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,637 - __main__ - INFO - 📊 并发进度: 50/56 (89.3%)
2025-07-03 14:52:17,637 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA'} 模拟完成: 0.0000 (1.10s)
2025-07-03 14:52:17,637 - __main__ - INFO - 周夏普比率: 0.0000
2025-07-03 14:52:17,638 - __main__ - INFO - 交易天数: 5
2025-07-03 14:52:17,637 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'BeOA', 'NOA'} 模拟完成: 0.0000 (1.12s)
2025-07-03 14:52:17,638 - __main__ - DEBUG - ✅ 联盟 {'TRA', 'TAA', 'NOA'} 模拟完成: 0.0000 (1.07s)
2025-07-03 14:52:17,638 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'TRA', 'TAA'} 模拟完成: 0.0000 (1.09s)
2025-07-03 14:52:17,638 - __main__ - INFO - 相比历史均值 (0.0000)：下降
2025-07-03 14:52:17,638 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,638 - __main__ - INFO - 联盟 {'NAA', 'FAA', 'TRA', 'TAA', 'NOA'} 模拟完成: 夏普比率 = 0.0000, 完成周数 = 4
2025-07-03 14:52:17,638 - __main__ - DEBUG - ✅ 联盟 {'NAA', 'FAA', 'TRA', 'TAA', 'NOA'} 模拟完成: 0.0000 (1.11s)
2025-07-03 14:52:17,638 - __main__ - INFO - 📊 并发进度: 56/56 (100.0%)
2025-07-03 14:52:17,639 - __main__ - INFO - ✅ 并发交易模拟完成: 成功 56 个，失败 0 个，总耗时 2.94s
2025-07-03 14:52:17,639 - __main__ - INFO - ==================================================
2025-07-03 14:52:17,639 - __main__ - INFO - 阶段3.5: 周期性Shapley值计算
2025-07-03 14:52:17,640 - __main__ - INFO - 📅 日期范围详细分析:
2025-07-03 14:52:17,640 - __main__ - INFO -   总天数: 31
2025-07-03 14:52:17,640 - __main__ - INFO -   周末天数: 9
2025-07-03 14:52:17,640 - __main__ - INFO -   节假日天数: 2
2025-07-03 14:52:17,640 - __main__ - INFO -   交易天数: 20
2025-07-03 14:52:17,640 - __main__ - INFO - 根据真实日历计算: 2023-01-01 到 2023-01-31, 实际交易天数: 20
2025-07-03 14:52:17,640 - __main__ - INFO - 开始周期性Shapley值计算阶段...
2025-07-03 14:52:17,640 - __main__ - INFO - 实际数据天数: 20, 计算周期数: 4
2025-07-03 14:52:17,640 - __main__ - INFO - 计算第 1 周 (第 1-5 天) 的Shapley值
2025-07-03 14:52:17,641 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-03 14:52:17,641 - __main__ - INFO - 已提供 56 个联盟的特征函数值
2025-07-03 14:52:17,641 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-03 14:52:17,641 - __main__ - INFO - 联盟值补全完成: 已提供 56 个，补全 72 个
2025-07-03 14:52:17,641 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-03 14:52:17,641 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-03 14:52:17,641 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-03 14:52:17,641 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-03 14:52:17,641 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-03 14:52:17,641 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-03 14:52:17,641 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-03 14:52:17,641 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-03 14:52:17,641 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-03 14:52:17,641 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-03 14:52:17,642 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-03 14:52:17,642 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-03 14:52:17,642 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-03 14:52:17,642 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-03 14:52:17,642 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-03 14:52:17,642 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-03 14:52:17,642 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-03 14:52:17,642 - __main__ - INFO - 第 1 周Shapley值计算完成:
2025-07-03 14:52:17,642 - __main__ - INFO -   NAA: 0.000000
2025-07-03 14:52:17,642 - __main__ - INFO -   TAA: 0.000000
2025-07-03 14:52:17,642 - __main__ - INFO -   FAA: 0.000000
2025-07-03 14:52:17,642 - __main__ - INFO -   BOA: 0.000000
2025-07-03 14:52:17,642 - __main__ - INFO -   BeOA: 0.000000
2025-07-03 14:52:17,642 - __main__ - INFO -   NOA: 0.000000
2025-07-03 14:52:17,642 - __main__ - INFO -   TRA: 0.000000
2025-07-03 14:52:17,642 - __main__ - INFO - 计算第 2 周 (第 6-10 天) 的Shapley值
2025-07-03 14:52:17,643 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-03 14:52:17,643 - __main__ - INFO - 已提供 56 个联盟的特征函数值
2025-07-03 14:52:17,643 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-03 14:52:17,643 - __main__ - INFO - 联盟值补全完成: 已提供 56 个，补全 72 个
2025-07-03 14:52:17,643 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-03 14:52:17,643 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-03 14:52:17,643 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-03 14:52:17,643 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-03 14:52:17,643 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-03 14:52:17,643 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-03 14:52:17,643 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-03 14:52:17,643 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-03 14:52:17,643 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-03 14:52:17,643 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-03 14:52:17,643 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-03 14:52:17,643 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-03 14:52:17,643 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-03 14:52:17,643 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-03 14:52:17,643 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-03 14:52:17,643 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-03 14:52:17,643 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-03 14:52:17,643 - __main__ - INFO - 第 2 周Shapley值计算完成:
2025-07-03 14:52:17,643 - __main__ - INFO -   NAA: 0.000000
2025-07-03 14:52:17,643 - __main__ - INFO -   TAA: 0.000000
2025-07-03 14:52:17,643 - __main__ - INFO -   FAA: 0.000000
2025-07-03 14:52:17,643 - __main__ - INFO -   BOA: 0.000000
2025-07-03 14:52:17,643 - __main__ - INFO -   BeOA: 0.000000
2025-07-03 14:52:17,643 - __main__ - INFO -   NOA: 0.000000
2025-07-03 14:52:17,643 - __main__ - INFO -   TRA: 0.000000
2025-07-03 14:52:17,643 - __main__ - INFO - 计算第 3 周 (第 11-15 天) 的Shapley值
2025-07-03 14:52:17,644 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-03 14:52:17,644 - __main__ - INFO - 已提供 56 个联盟的特征函数值
2025-07-03 14:52:17,644 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-03 14:52:17,644 - __main__ - INFO - 联盟值补全完成: 已提供 56 个，补全 72 个
2025-07-03 14:52:17,644 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-03 14:52:17,644 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-03 14:52:17,644 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-03 14:52:17,644 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-03 14:52:17,644 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-03 14:52:17,644 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-03 14:52:17,645 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-03 14:52:17,645 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-03 14:52:17,645 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-03 14:52:17,645 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-03 14:52:17,645 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-03 14:52:17,645 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-03 14:52:17,645 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-03 14:52:17,645 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-03 14:52:17,645 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-03 14:52:17,645 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-03 14:52:17,645 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-03 14:52:17,645 - __main__ - INFO - 第 3 周Shapley值计算完成:
2025-07-03 14:52:17,645 - __main__ - INFO -   NAA: 0.000000
2025-07-03 14:52:17,645 - __main__ - INFO -   TAA: 0.000000
2025-07-03 14:52:17,645 - __main__ - INFO -   FAA: 0.000000
2025-07-03 14:52:17,645 - __main__ - INFO -   BOA: 0.000000
2025-07-03 14:52:17,645 - __main__ - INFO -   BeOA: 0.000000
2025-07-03 14:52:17,645 - __main__ - INFO -   NOA: 0.000000
2025-07-03 14:52:17,645 - __main__ - INFO -   TRA: 0.000000
2025-07-03 14:52:17,645 - __main__ - INFO - 计算第 4 周 (第 16-20 天) 的Shapley值
2025-07-03 14:52:17,646 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-03 14:52:17,646 - __main__ - INFO - 已提供 56 个联盟的特征函数值
2025-07-03 14:52:17,646 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-03 14:52:17,646 - __main__ - INFO - 联盟值补全完成: 已提供 56 个，补全 72 个
2025-07-03 14:52:17,646 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-03 14:52:17,646 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-03 14:52:17,646 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-03 14:52:17,646 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-03 14:52:17,646 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-03 14:52:17,646 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-03 14:52:17,646 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-03 14:52:17,646 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-03 14:52:17,646 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-03 14:52:17,646 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-03 14:52:17,646 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-03 14:52:17,646 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-03 14:52:17,646 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-03 14:52:17,646 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-03 14:52:17,646 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-03 14:52:17,646 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-03 14:52:17,646 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-03 14:52:17,646 - __main__ - INFO - 第 4 周Shapley值计算完成:
2025-07-03 14:52:17,646 - __main__ - INFO -   NAA: 0.000000
2025-07-03 14:52:17,646 - __main__ - INFO -   TAA: 0.000000
2025-07-03 14:52:17,646 - __main__ - INFO -   FAA: 0.000000
2025-07-03 14:52:17,646 - __main__ - INFO -   BOA: 0.000000
2025-07-03 14:52:17,646 - __main__ - INFO -   BeOA: 0.000000
2025-07-03 14:52:17,646 - __main__ - INFO -   NOA: 0.000000
2025-07-03 14:52:17,646 - __main__ - INFO -   TRA: 0.000000
2025-07-03 14:52:17,647 - __main__ - INFO - 周期性Shapley值结果已保存至: results/periodic_shapley/periodic_shapley_20250703_145217.json
2025-07-03 14:52:17,647 - __main__ - INFO - ======================================================================
2025-07-03 14:52:17,647 - __main__ - INFO - 周期性Shapley值计算结果汇总
2025-07-03 14:52:17,647 - __main__ - INFO - ======================================================================
2025-07-03 14:52:17,647 - __main__ - INFO - 总周数: 4
2025-07-03 14:52:17,647 - __main__ - INFO - 成功计算: 4 周
2025-07-03 14:52:17,647 - __main__ - INFO - 计算失败: 0 周
2025-07-03 14:52:17,647 - __main__ - INFO - 
各智能体平均贡献度:
2025-07-03 14:52:17,647 - __main__ - INFO -   NAA: 0.000000
2025-07-03 14:52:17,647 - __main__ - INFO -   TAA: 0.000000
2025-07-03 14:52:17,648 - __main__ - INFO -   FAA: 0.000000
2025-07-03 14:52:17,648 - __main__ - INFO -   BOA: 0.000000
2025-07-03 14:52:17,648 - __main__ - INFO -   BeOA: 0.000000
2025-07-03 14:52:17,648 - __main__ - INFO -   NOA: 0.000000
2025-07-03 14:52:17,648 - __main__ - INFO -   TRA: 0.000000
2025-07-03 14:52:17,648 - __main__ - INFO - 
表现最佳: NAA (0.000000)
2025-07-03 14:52:17,648 - __main__ - INFO - 表现最差: NAA (0.000000)
2025-07-03 14:52:17,648 - __main__ - INFO - 
贡献度趋势分析:
2025-07-03 14:52:17,648 - __main__ - INFO -   NAA: 稳定 (变化幅度: 0.000000)
2025-07-03 14:52:17,648 - __main__ - INFO -   TAA: 稳定 (变化幅度: 0.000000)
2025-07-03 14:52:17,648 - __main__ - INFO -   FAA: 稳定 (变化幅度: 0.000000)
2025-07-03 14:52:17,648 - __main__ - INFO -   BOA: 稳定 (变化幅度: 0.000000)
2025-07-03 14:52:17,648 - __main__ - INFO -   BeOA: 稳定 (变化幅度: 0.000000)
2025-07-03 14:52:17,648 - __main__ - INFO -   NOA: 稳定 (变化幅度: 0.000000)
2025-07-03 14:52:17,648 - __main__ - INFO -   TRA: 稳定 (变化幅度: 0.000000)
2025-07-03 14:52:17,648 - __main__ - INFO - ======================================================================
2025-07-03 14:52:17,648 - __main__ - INFO - 周期性Shapley值计算阶段完成: 计算了 4 个周期
2025-07-03 14:52:17,648 - __main__ - INFO - ==================================================
2025-07-03 14:52:17,648 - __main__ - INFO - 阶段4: 最终Shapley值计算
2025-07-03 14:52:17,648 - __main__ - INFO - 开始Shapley值计算阶段...
2025-07-03 14:52:17,648 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-03 14:52:17,648 - __main__ - INFO - 已提供 56 个联盟的特征函数值
2025-07-03 14:52:17,648 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-03 14:52:17,648 - __main__ - INFO - 联盟值补全完成: 已提供 56 个，补全 72 个
2025-07-03 14:52:17,648 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-03 14:52:17,648 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-03 14:52:17,648 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.000000
2025-07-03 14:52:17,648 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-03 14:52:17,648 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.000000
2025-07-03 14:52:17,648 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-03 14:52:17,648 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.000000
2025-07-03 14:52:17,648 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-03 14:52:17,648 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.000000
2025-07-03 14:52:17,648 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-03 14:52:17,648 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.000000
2025-07-03 14:52:17,648 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-03 14:52:17,648 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.000000
2025-07-03 14:52:17,648 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-03 14:52:17,648 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.000000
2025-07-03 14:52:17,649 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.000000 = 大联盟值 0.000000
2025-07-03 14:52:17,649 - __main__ - INFO - Shapley值计算完成，耗时 0.001s
2025-07-03 14:52:17,649 - __main__ - INFO - Shapley值计算阶段完成: 计算了 7 个智能体的贡献度
2025-07-03 14:52:17,649 - __main__ - INFO - ==================================================
2025-07-03 14:52:17,649 - __main__ - INFO - 贡献度评估完成，总耗时: 35.05s
2025-07-03 14:52:17,649 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,649 - __main__ - INFO - 步骤3: 评估后OPRO优化
2025-07-03 14:52:17,649 - __main__ - INFO - ============================================================
2025-07-03 14:52:17,649 - __main__ - INFO - 开始OPRO优化循环: 7 个智能体
2025-07-03 14:52:17,649 - __main__ - INFO - 开始批量优化 7 个智能体
2025-07-03 14:52:17,649 - __main__ - INFO - 开始为智能体 NAA 优化提示词
2025-07-03 14:52:17,649 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-03 14:52:17,649 - __main__ - DEBUG - 获取智能体 NAA 最近 10 周的历史数据
2025-07-03 14:52:17,649 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-03 14:52:17,649 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:52:17,649 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:52:17,649 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:52:17,650 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:52:17,650 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:52:17,650 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:52:17,650 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:52:17,650 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:52:26,136 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:52:26 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145218542eb2759d1e4494'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:52:26,138 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:52:26,138 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:52:26,138 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:52:26,138 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:52:26,138 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:52:26,138 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:52:26,139 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:52:26,139 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-03 14:52:26,139 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-03 14:52:26,140 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:52:26,140 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:52:26,140 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:52:26,140 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:52:26,141 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:52:26,141 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:52:26,141 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:52:26,141 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:52:31,678 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:52:31 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145226081bcced5b6c4fcd'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:52:31,681 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:52:31,682 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:52:31,682 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:52:31,682 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:52:31,683 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:52:31,683 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:52:31,685 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:52:31,685 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-03 14:52:31,686 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-03 14:52:31,686 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:52:31,686 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:52:31,686 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:52:31,687 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:52:31,687 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:52:31,687 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:52:31,687 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:52:31,687 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:52:37,274 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:52:37 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145232a350741f0ef74e69'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:52:37,276 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:52:37,276 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:52:37,277 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:52:37,277 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:52:37,277 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:52:37,277 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:52:37,278 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:52:37,278 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-03 14:52:37,278 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-03 14:52:37,278 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:52:37,278 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:52:37,278 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:52:37,279 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:52:37,279 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:52:37,279 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:52:37,280 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:52:37,280 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:52:39,978 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:52:40 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145237e8fabb3fa4514a89'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:52:39,979 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:52:39,979 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:52:39,979 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:52:39,979 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:52:39,979 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:52:39,979 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:52:39,980 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:52:39,980 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-03 14:52:39,980 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-03 14:52:39,980 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:52:39,980 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:52:39,980 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:52:39,981 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:52:39,981 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:52:39,981 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:52:39,982 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:52:39,982 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:52:43,329 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:52:43 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031452407e33a2c443864572'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:52:43,331 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:52:43,331 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:52:43,332 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:52:43,332 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:52:43,332 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:52:43,332 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:52:43,334 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:52:43,334 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-03 14:52:43,334 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-03 14:52:43,334 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:52:43,334 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:52:43,334 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:52:43,335 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:52:43,335 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:52:43,335 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:52:43,335 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:52:43,335 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:52:45,749 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:52:46 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031452435c28cb10ea2b4f90'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:52:45,749 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:52:45,750 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:52:45,750 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:52:45,751 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:52:45,751 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:52:45,751 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:52:45,752 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:52:45,752 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-03 14:52:45,752 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-03 14:52:45,752 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:52:45,752 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:52:45,752 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:52:45,753 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:52:45,753 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:52:45,753 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:52:45,753 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:52:45,753 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:52:50,469 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:52:50 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145246b880db604ce0484e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:52:50,470 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:52:50,470 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:52:50,470 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:52:50,470 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:52:50,470 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:52:50,470 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:52:50,471 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:52:50,471 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-03 14:52:50,471 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-03 14:52:50,471 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:52:50,471 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:52:50,471 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:52:50,472 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:52:50,472 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:52:50,472 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:52:50,472 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:52:50,472 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:52:53,578 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:52:53 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070314525075c981e9c4424306'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:52:53,579 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:52:53,579 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:52:53,579 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:52:53,580 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:52:53,580 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:52:53,580 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:52:53,582 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:52:53,582 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-03 14:52:53,582 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-03 14:52:53,582 - __main__ - ERROR - 优化智能体 NAA 失败: 'parallel_evaluation'
2025-07-03 14:52:53,582 - __main__ - WARNING - ❌ NAA 优化失败: 'parallel_evaluation'
2025-07-03 14:52:53,582 - __main__ - INFO - 开始为智能体 TAA 优化提示词
2025-07-03 14:52:53,582 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-03 14:52:53,583 - __main__ - DEBUG - 获取智能体 TAA 最近 10 周的历史数据
2025-07-03 14:52:53,584 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-03 14:52:53,584 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:52:53,584 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:52:53,584 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:52:53,584 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:52:53,585 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:52:53,585 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:52:53,585 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:52:53,585 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:52:58,103 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:52:58 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070314525372ac611ab75d4ca6'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:52:58,103 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:52:58,103 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:52:58,104 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:52:58,104 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:52:58,104 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:52:58,104 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:52:58,104 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:52:58,104 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-03 14:52:58,105 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-03 14:52:58,105 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:52:58,105 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:52:58,105 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:52:58,105 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:52:58,105 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:52:58,105 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:52:58,106 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:52:58,106 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:00,903 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:01 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070314525842798789f49c4c23'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:00,904 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:00,905 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:00,905 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:00,905 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:00,905 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:00,905 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:00,906 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:00,907 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-03 14:53:00,907 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-03 14:53:00,907 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:00,907 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:00,907 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:00,908 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:00,908 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:00,908 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:00,908 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:00,908 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:06,256 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:06 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070314530195b182a3ee794d55'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:06,259 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:06,259 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:06,259 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:06,259 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:06,259 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:06,259 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:06,260 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:06,260 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-03 14:53:06,260 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-03 14:53:06,260 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:06,260 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:06,260 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:06,261 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:06,261 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:06,261 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:06,261 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:06,261 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:10,952 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:11 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031453066cf7a6ed110a405b'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:10,954 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:10,954 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:10,955 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:10,955 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:10,955 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:10,955 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:10,956 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:10,956 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-03 14:53:10,956 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-03 14:53:10,956 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:10,956 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:10,956 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:10,956 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:10,956 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:10,956 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:10,957 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:10,957 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:14,072 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:14 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031453118a90e89be3bf44ed'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:14,072 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:14,073 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:14,073 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:14,073 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:14,073 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:14,073 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:14,075 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:14,075 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-03 14:53:14,076 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-03 14:53:14,076 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:14,076 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:14,076 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:14,076 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:14,076 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:14,076 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:14,077 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:14,077 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:17,158 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:17 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031453148f2b5182b4ea436d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:17,159 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:17,159 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:17,159 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:17,159 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:17,159 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:17,159 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:17,160 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:17,161 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-03 14:53:17,161 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-03 14:53:17,161 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:17,161 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:17,161 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:17,162 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:17,162 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:17,162 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:17,162 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:17,162 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:19,654 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:19 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145317c59b31e7612c4600'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:19,655 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:19,655 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:19,655 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:19,655 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:19,655 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:19,655 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:19,655 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:19,656 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-03 14:53:19,656 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-03 14:53:19,656 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:19,656 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:19,656 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:19,656 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:19,656 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:19,656 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:19,656 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:19,656 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:22,874 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:23 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031453208f7808f343de4661'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:22,875 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:22,875 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:22,875 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:22,876 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:22,876 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:22,876 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:22,877 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:22,877 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-03 14:53:22,877 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-03 14:53:22,877 - __main__ - ERROR - 优化智能体 TAA 失败: 'parallel_evaluation'
2025-07-03 14:53:22,877 - __main__ - WARNING - ❌ TAA 优化失败: 'parallel_evaluation'
2025-07-03 14:53:22,877 - __main__ - INFO - 开始为智能体 FAA 优化提示词
2025-07-03 14:53:22,877 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-03 14:53:22,880 - __main__ - DEBUG - 获取智能体 FAA 最近 10 周的历史数据
2025-07-03 14:53:22,880 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-03 14:53:22,880 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:22,880 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:22,880 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:22,881 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:22,881 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:22,881 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:22,881 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:22,881 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:25,549 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:25 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145323d07b1ca03a004718'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:25,551 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:25,552 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:25,552 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:25,553 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:25,553 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:25,553 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:25,555 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:25,555 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-03 14:53:25,555 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-03 14:53:25,555 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:25,555 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:25,555 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:25,556 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:25,556 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:25,557 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:25,557 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:25,557 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:27,213 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:27 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145325e118bfcd69e54472'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:27,213 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:27,213 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:27,213 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:27,213 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:27,213 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:27,213 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:27,215 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:27,215 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-03 14:53:27,215 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-03 14:53:27,215 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:27,215 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:27,215 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:27,216 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:27,216 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:27,216 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:27,217 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:27,217 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:30,905 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:31 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031453276e6647632d7b42fb'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:30,906 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:30,906 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:30,906 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:30,906 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:30,906 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:30,906 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:30,907 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:30,907 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-03 14:53:30,907 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-03 14:53:30,907 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:30,907 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:30,907 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:30,907 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:30,907 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:30,907 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:30,907 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:30,907 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:34,513 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:34 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031453314378eb94dcac40f1'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:34,514 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:34,514 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:34,514 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:34,515 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:34,515 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:34,515 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:34,516 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:34,516 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-03 14:53:34,516 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-03 14:53:34,516 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:34,516 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:34,516 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:34,517 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:34,517 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:34,517 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:34,517 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:34,517 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:38,174 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:38 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145334020f5aa5884349df'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:38,175 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:38,176 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:38,176 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:38,176 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:38,176 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:38,177 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:38,179 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:38,179 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-03 14:53:38,179 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-03 14:53:38,179 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:38,179 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:38,180 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:38,181 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:38,181 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:38,181 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:38,181 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:38,181 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 14:53:40,044 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 06:53:40 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703145338920b712ca1cf4310'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 14:53:40,044 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 14:53:40,045 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 14:53:40,045 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 14:53:40,045 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 14:53:40,045 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 14:53:40,045 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 14:53:40,047 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 14:53:40,047 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-03 14:53:40,047 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-03 14:53:40,047 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 14:53:40,047 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 14:53:40,047 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 14:53:40,048 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 14:53:40,048 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 14:53:40,048 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 14:53:40,048 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 14:53:40,048 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
