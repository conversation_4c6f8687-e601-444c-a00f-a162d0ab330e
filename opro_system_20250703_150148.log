2025-07-03 15:01:48,602 - __main__ - INFO - ====================================================================================================
2025-07-03 15:01:48,602 - __main__ - INFO - OPRO系统启动
2025-07-03 15:01:48,603 - __main__ - INFO - ====================================================================================================
2025-07-03 15:01:48,603 - __main__ - INFO - 运行模式: optimization
2025-07-03 15:01:48,603 - __main__ - INFO - LLM提供商: zhipuai
2025-07-03 15:01:48,603 - __main__ - INFO - OPRO启用: True
2025-07-03 15:01:48,603 - __main__ - INFO - 初始化系统...
2025-07-03 15:01:48,603 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-03 15:01:48,698 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-03 15:01:48,698 - __main__ - INFO - 分析缓存初始化完成
2025-07-03 15:01:48,698 - __main__ - INFO - 联盟管理器初始化完成
2025-07-03 15:01:48,698 - __main__ - INFO - 交易模拟器初始化完成
2025-07-03 15:01:48,698 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-03 15:01:48,698 - __main__ - DEBUG - 所有核心模块初始化完成
2025-07-03 15:01:48,698 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-03 15:01:48,716 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-03 15:01:48,716 - __main__ - INFO - 数据库初始化完成
2025-07-03 15:01:48,717 - __main__ - DEBUG - 刷新Shapley值缓存...
2025-07-03 15:01:48,740 - __main__ - DEBUG - 缓存刷新完成，共加载 7 个智能体的数据
2025-07-03 15:01:48,740 - __main__ - INFO - 最新Shapley数据加载完成
2025-07-03 15:01:48,740 - __main__ - INFO - 历史得分管理器初始化完成
2025-07-03 15:01:48,740 - __main__ - INFO - OPRO优化器初始化完成
2025-07-03 15:01:48,740 - __main__ - INFO - OPRO组件初始化成功
2025-07-03 15:01:48,740 - __main__ - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-03 15:01:48,740 - __main__ - INFO - 系统初始化完成
2025-07-03 15:01:48,740 - __main__ - INFO - ================================================================================
2025-07-03 15:01:48,740 - __main__ - INFO - 运行模式: OPRO优化
2025-07-03 15:01:48,740 - __main__ - INFO - ================================================================================
2025-07-03 15:01:48,740 - __main__ - INFO - 开始OPRO优化循环...
2025-07-03 15:01:48,740 - __main__ - INFO - 开始OPRO优化循环: 2 个智能体
2025-07-03 15:01:48,740 - __main__ - INFO - 开始批量优化 2 个智能体
2025-07-03 15:01:48,740 - __main__ - INFO - 开始为智能体 TAA 优化提示词
2025-07-03 15:01:48,740 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-03 15:01:48,741 - __main__ - DEBUG - 获取智能体 TAA 最近 10 周的历史数据
2025-07-03 15:01:48,741 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-03 15:01:48,741 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:01:48,741 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:01:48,741 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:01:48,741 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=8.0 socket_options=None
2025-07-03 15:01:48,743 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x144d7f320>
2025-07-03 15:01:48,743 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-07-03 15:01:48,743 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:01:48,743 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-07-03 15:01:48,743 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:01:48,743 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-03 15:01:48,743 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-03 15:01:48,744 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x144d563d0> server_hostname='open.bigmodel.cn' timeout=8.0
2025-07-03 15:01:48,972 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x144d7d580>
2025-07-03 15:01:48,973 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:01:48,973 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:01:48,973 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:01:48,973 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:01:48,973 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:01:53,401 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:01:53 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=1a0c66d117515261093251850e005f7f60e885d623f5c953baaef0de3ad3a8;path=/;HttpOnly;Max-Age=1800'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070315014921197f414e724769'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:01:53,403 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:01:53,403 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:01:53,404 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:01:53,404 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:01:53,404 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:01:53,404 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:01:53,406 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:01:53,406 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-03 15:01:53,406 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-03 15:01:53,406 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:01:53,406 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:01:53,406 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:01:53,407 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:01:53,407 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:01:53,407 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:01:53,407 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:01:53,407 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:01:56,806 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:01:56 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070315015353cd068debea4db9'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:01:56,806 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:01:56,806 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:01:56,807 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:01:56,807 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:01:56,807 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:01:56,807 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:01:56,808 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:01:56,808 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-03 15:01:56,808 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-03 15:01:56,808 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:01:56,808 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:01:56,808 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:01:56,809 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:01:56,809 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:01:56,809 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:01:56,809 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:01:56,809 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:00,038 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:00 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070315015760af4cabab6e472b'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:00,039 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:00,039 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:00,039 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:00,039 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:00,039 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:00,039 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:00,041 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:00,042 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-03 15:02:00,042 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-03 15:02:00,042 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:00,042 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:00,042 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:00,043 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:00,043 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:00,043 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:00,043 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:00,043 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:05,905 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:06 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070315020072044d7ee9e44f2d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:05,906 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:05,906 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:05,907 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:05,907 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:05,907 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:05,907 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:05,909 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:05,909 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-03 15:02:05,909 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-03 15:02:05,909 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:05,909 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:05,909 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:05,910 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:05,911 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:05,911 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:05,911 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:05,911 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:10,787 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:10 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703150206a8d05a2a96dd4357'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:10,788 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:10,789 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:10,790 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:10,790 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:10,790 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:10,790 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:10,792 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:10,792 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-03 15:02:10,793 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-03 15:02:10,793 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:10,793 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:10,793 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:10,794 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:10,794 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:10,794 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:10,794 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:10,794 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:13,753 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:13 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070315021158b6c6382cb840ed'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:13,753 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:13,753 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:13,753 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:13,753 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:13,753 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:13,753 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:13,754 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:13,754 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-03 15:02:13,754 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-03 15:02:13,754 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:13,754 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:13,754 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:13,755 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:13,755 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:13,755 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:13,755 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:13,755 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:19,786 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:19 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703150214ece0f563ee9141c4'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:19,786 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:19,786 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:19,787 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:19,787 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:19,787 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:19,787 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:19,788 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:19,788 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-03 15:02:19,788 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-03 15:02:19,788 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:19,788 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:19,788 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:19,789 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:19,789 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:19,790 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:19,790 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:19,790 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:22,355 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:22 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703150220646c68e07a6e4af8'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:22,356 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:22,356 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:22,356 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:22,356 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:22,356 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:22,356 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:22,357 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:22,357 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-03 15:02:22,357 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-03 15:02:22,360 - __main__ - INFO - 智能体 TAA 优化完成，最佳候选预期得分: 0.951895
2025-07-03 15:02:22,360 - __main__ - INFO - ✅ TAA 优化成功，预期改进: 0.896371
2025-07-03 15:02:22,360 - __main__ - INFO - 开始为智能体 TRA 优化提示词
2025-07-03 15:02:22,360 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-03 15:02:22,360 - __main__ - DEBUG - 获取智能体 TRA 最近 10 周的历史数据
2025-07-03 15:02:22,360 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-03 15:02:22,360 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:22,360 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:22,360 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:22,361 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:22,361 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:22,361 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:22,361 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:22,361 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:24,259 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:24 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703150222af7cb4208e6e4501'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:24,260 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:24,260 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:24,260 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:24,260 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:24,260 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:24,260 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:24,261 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:24,261 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-03 15:02:24,261 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-03 15:02:24,261 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:24,261 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:24,261 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:24,262 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:24,262 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:24,262 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:24,262 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:24,262 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:26,513 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:26 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703150224dab2a772af6a4c7c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:26,513 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:26,513 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:26,514 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:26,514 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:26,514 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:26,514 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:26,515 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:26,515 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-03 15:02:26,515 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-03 15:02:26,515 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:26,515 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:26,515 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:26,516 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:26,516 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:26,516 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:26,516 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:26,516 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:29,689 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:29 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703150226011df44564204063'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:29,689 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:29,689 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:29,690 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:29,690 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:29,690 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:29,690 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:29,691 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:29,691 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-03 15:02:29,691 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-03 15:02:29,691 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:29,691 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:29,691 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:29,692 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:29,692 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:29,692 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:29,692 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:29,692 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:33,651 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:33 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031502290f2ab98dced74d7f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:33,651 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:33,651 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:33,652 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:33,652 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:33,652 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:33,652 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:33,653 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:33,653 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-03 15:02:33,653 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-03 15:02:33,653 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:33,653 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:33,653 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:33,654 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:33,654 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:33,654 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:33,654 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:33,654 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:36,622 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:36 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703150233e0c4956523b24bbb'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:36,623 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:36,623 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:36,623 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:36,623 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:36,623 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:36,623 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:36,623 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:36,624 - __main__ - DEBUG - 生成有效候选 5/8
2025-07-03 15:02:36,624 - __main__ - DEBUG - 生成候选提示词 6/16
2025-07-03 15:02:36,624 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:36,624 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:36,624 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:36,624 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:36,624 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:36,624 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:36,624 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:36,624 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:39,133 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:39 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031502369eb36b7455dc4bbd'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:39,133 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:39,134 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:39,134 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:39,134 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:39,134 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:39,134 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:39,135 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:39,135 - __main__ - DEBUG - 生成有效候选 6/8
2025-07-03 15:02:39,135 - __main__ - DEBUG - 生成候选提示词 7/16
2025-07-03 15:02:39,135 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:39,135 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:39,135 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:39,135 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:39,136 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:39,136 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:39,136 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:39,136 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:42,940 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:43 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031502392ccfd3b1199f431c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:42,941 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:42,941 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:42,941 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:42,941 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:42,941 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:42,941 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:42,942 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:42,942 - __main__ - DEBUG - 生成有效候选 7/8
2025-07-03 15:02:42,942 - __main__ - DEBUG - 生成候选提示词 8/16
2025-07-03 15:02:42,942 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 15:02:42,942 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 15:02:42,942 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 15:02:42,942 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 15:02:42,942 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 15:02:42,942 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 15:02:42,942 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 15:02:42,942 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 15:02:45,910 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 07:02:46 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703150243ace350f29bb344fd'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 15:02:45,911 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 15:02:45,911 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 15:02:45,911 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 15:02:45,911 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 15:02:45,911 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 15:02:45,911 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 15:02:45,912 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 15:02:45,912 - __main__ - DEBUG - 生成有效候选 8/8
2025-07-03 15:02:45,912 - __main__ - INFO - 成功生成 8 个候选提示词
2025-07-03 15:02:45,913 - __main__ - INFO - 智能体 TRA 优化完成，最佳候选预期得分: 0.714481
2025-07-03 15:02:45,913 - __main__ - INFO - ✅ TRA 优化成功，预期改进: 0.714481
2025-07-03 15:02:45,913 - __main__ - INFO - 批量优化完成: 2/2 成功，成功率: 100.0%，总耗时: 57.17s
2025-07-03 15:02:45,914 - __main__ - INFO - 优化结果已存储: TAA -> 82c106fd...
2025-07-03 15:02:45,915 - __main__ - INFO - 优化结果已存储: TRA -> 10ec7a3c...
2025-07-03 15:02:45,915 - __main__ - INFO - OPRO优化循环完成: 2/2 智能体优化成功
2025-07-03 15:02:45,915 - __main__ - INFO - ====================================================================================================
2025-07-03 15:02:45,915 - __main__ - INFO - 🎉 执行成功!
2025-07-03 15:02:45,915 - __main__ - INFO - ====================================================================================================
