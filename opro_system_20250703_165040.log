2025-07-03 16:50:40,507 - __main__ - INFO - ====================================================================================================
2025-07-03 16:50:40,507 - __main__ - INFO - OPRO系统启动
2025-07-03 16:50:40,507 - __main__ - INFO - ====================================================================================================
2025-07-03 16:50:40,507 - __main__ - INFO - 运行模式: optimization
2025-07-03 16:50:40,507 - __main__ - INFO - LLM提供商: zhipuai
2025-07-03 16:50:40,507 - __main__ - INFO - OPRO启用: True
2025-07-03 16:50:40,507 - __main__ - INFO - 初始化系统...
2025-07-03 16:50:40,507 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-03 16:50:40,585 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-03 16:50:40,585 - __main__ - INFO - 分析缓存初始化完成
2025-07-03 16:50:40,585 - __main__ - INFO - 联盟管理器初始化完成
2025-07-03 16:50:40,585 - __main__ - INFO - 交易模拟器初始化完成
2025-07-03 16:50:40,585 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-03 16:50:40,585 - __main__ - DEBUG - 所有核心模块初始化完成
2025-07-03 16:50:40,585 - __main__ - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-03 16:50:40,601 - __main__ - INFO - ZhipuAI 客户端初始化成功
2025-07-03 16:50:40,602 - __main__ - INFO - 数据库初始化完成
2025-07-03 16:50:40,602 - __main__ - DEBUG - 刷新Shapley值缓存...
2025-07-03 16:50:40,617 - __main__ - DEBUG - 缓存刷新完成，共加载 7 个智能体的数据
2025-07-03 16:50:40,617 - __main__ - INFO - 最新Shapley数据加载完成
2025-07-03 16:50:40,617 - __main__ - INFO - 历史得分管理器初始化完成
2025-07-03 16:50:40,617 - __main__ - INFO - OPRO优化器初始化完成
2025-07-03 16:50:40,617 - __main__ - INFO - OPRO组件初始化成功
2025-07-03 16:50:40,617 - __main__ - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-03 16:50:40,617 - __main__ - INFO - 系统初始化完成
2025-07-03 16:50:40,617 - __main__ - INFO - ================================================================================
2025-07-03 16:50:40,617 - __main__ - INFO - 运行模式: OPRO优化
2025-07-03 16:50:40,617 - __main__ - INFO - ================================================================================
2025-07-03 16:50:40,617 - __main__ - INFO - 开始OPRO优化循环...
2025-07-03 16:50:40,617 - __main__ - INFO - 开始OPRO优化循环: 7 个智能体
2025-07-03 16:50:40,617 - __main__ - INFO - 开始批量优化 7 个智能体
2025-07-03 16:50:40,617 - __main__ - INFO - 开始为智能体 NAA 优化提示词
2025-07-03 16:50:40,617 - __main__ - INFO - 生成 8 个候选提示词...
2025-07-03 16:50:40,617 - __main__ - DEBUG - 获取智能体 NAA 最近 10 周的历史数据
2025-07-03 16:50:40,617 - __main__ - DEBUG - 生成候选提示词 1/16
2025-07-03 16:50:40,618 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 16:50:40,618 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 16:50:40,618 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 16:50:40,618 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=8.0 socket_options=None
2025-07-03 16:50:40,619 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x137eaa480>
2025-07-03 16:50:40,619 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-07-03 16:50:40,619 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 16:50:40,619 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-07-03 16:50:40,619 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 16:50:40,619 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-03 16:50:40,619 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-03 16:50:40,619 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x1421374d0> server_hostname='open.bigmodel.cn' timeout=8.0
2025-07-03 16:50:40,944 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x14216f980>
2025-07-03 16:50:40,944 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 16:50:40,944 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 16:50:40,945 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 16:50:40,945 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 16:50:40,945 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 16:50:53,706 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 08:50:53 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117515326411223579e007c88eb4f96113bd0ffdd6abdcd850a0fb0;path=/;HttpOnly;Max-Age=1800'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070316504113a9dc8a217c4c05'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 16:50:53,711 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 16:50:53,711 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 16:50:53,711 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 16:50:53,711 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 16:50:53,711 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 16:50:53,712 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 16:50:53,719 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 16:50:53,719 - __main__ - DEBUG - 生成有效候选 1/8
2025-07-03 16:50:53,719 - __main__ - DEBUG - 生成候选提示词 2/16
2025-07-03 16:50:53,719 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 16:50:53,719 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 16:50:53,719 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 16:50:53,722 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 16:50:53,722 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 16:50:53,722 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 16:50:53,722 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 16:50:53,723 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 16:51:14,256 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 08:51:14 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703165053ac451530670e4e26'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 16:51:14,260 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 16:51:14,262 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 16:51:14,263 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 16:51:14,263 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 16:51:14,263 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 16:51:14,264 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 16:51:14,271 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 16:51:14,271 - __main__ - DEBUG - 生成有效候选 2/8
2025-07-03 16:51:14,271 - __main__ - DEBUG - 生成候选提示词 3/16
2025-07-03 16:51:14,271 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 16:51:14,272 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 16:51:14,272 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 16:51:14,273 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 16:51:14,273 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 16:51:14,273 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 16:51:14,274 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 16:51:14,274 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 16:51:35,647 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 08:51:35 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703165114d0d510f882fd449c'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 16:51:35,647 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 16:51:35,648 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 16:51:35,648 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 16:51:35,648 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 16:51:35,648 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 16:51:35,648 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 16:51:35,651 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 16:51:35,651 - __main__ - DEBUG - 生成有效候选 3/8
2025-07-03 16:51:35,651 - __main__ - DEBUG - 生成候选提示词 4/16
2025-07-03 16:51:35,651 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 16:51:35,651 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 16:51:35,651 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 16:51:35,652 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 16:51:35,652 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 16:51:35,652 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 16:51:35,652 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 16:51:35,652 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 16:51:35,909 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 429, b'Too Many Requests', [(b'Date', b'Thu, 03 Jul 2025 08:51:36 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703165135f69aab23f4694361'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains')])
2025-07-03 16:51:35,909 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-03 16:51:35,909 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 16:51:35,909 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 16:51:35,909 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 16:51:35,909 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 16:51:35,909 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "429 Too Many Requests"
2025-07-03 16:51:35,910 - zhipuai.core._http_client - DEBUG - Encountered httpx.HTTPStatusError
Traceback (most recent call last):
  File "/Applications/anaconda3/lib/python3.12/site-packages/zhipuai/core/_http_client.py", line 561, in _request
    response.raise_for_status()
  File "/Applications/anaconda3/lib/python3.12/site-packages/httpx/_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '429 Too Many Requests' for url 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/429
2025-07-03 16:51:35,911 - zhipuai.core._http_client - DEBUG - Retrying due to status code 429
2025-07-03 16:51:35,911 - zhipuai.core._http_client - DEBUG - 2 retries left
2025-07-03 16:51:35,911 - zhipuai.core._http_client - INFO - Retrying request to /chat/completions in 0.885401 seconds
2025-07-03 16:51:36,798 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 16:51:36,798 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 16:51:36,798 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 16:51:36,798 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 16:51:36,799 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 16:51:37,017 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 429, b'Too Many Requests', [(b'Date', b'Thu, 03 Jul 2025 08:51:37 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'2025070316513639a9914631614ff5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains')])
2025-07-03 16:51:37,018 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-03 16:51:37,018 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 16:51:37,018 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 16:51:37,018 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 16:51:37,019 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 16:51:37,019 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "429 Too Many Requests"
2025-07-03 16:51:37,019 - zhipuai.core._http_client - DEBUG - Encountered httpx.HTTPStatusError
Traceback (most recent call last):
  File "/Applications/anaconda3/lib/python3.12/site-packages/zhipuai/core/_http_client.py", line 561, in _request
    response.raise_for_status()
  File "/Applications/anaconda3/lib/python3.12/site-packages/httpx/_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '429 Too Many Requests' for url 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/429

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Applications/anaconda3/lib/python3.12/site-packages/zhipuai/core/_http_client.py", line 561, in _request
    response.raise_for_status()
  File "/Applications/anaconda3/lib/python3.12/site-packages/httpx/_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '429 Too Many Requests' for url 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/429
2025-07-03 16:51:37,019 - zhipuai.core._http_client - DEBUG - Retrying due to status code 429
2025-07-03 16:51:37,019 - zhipuai.core._http_client - DEBUG - 1 retry left
2025-07-03 16:51:37,019 - zhipuai.core._http_client - INFO - Retrying request to /chat/completions in 1.552880 seconds
2025-07-03 16:51:38,575 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 16:51:38,575 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 16:51:38,575 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 16:51:38,575 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 16:51:38,575 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 16:51:38,984 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 429, b'Too Many Requests', [(b'Date', b'Thu, 03 Jul 2025 08:51:39 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507031651386995197797924f02'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains')])
2025-07-03 16:51:38,988 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-07-03 16:51:38,988 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 16:51:38,989 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 16:51:38,989 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 16:51:38,989 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 16:51:38,990 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "429 Too Many Requests"
2025-07-03 16:51:38,990 - zhipuai.core._http_client - DEBUG - Encountered httpx.HTTPStatusError
Traceback (most recent call last):
  File "/Applications/anaconda3/lib/python3.12/site-packages/zhipuai/core/_http_client.py", line 561, in _request
    response.raise_for_status()
  File "/Applications/anaconda3/lib/python3.12/site-packages/httpx/_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '429 Too Many Requests' for url 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/429

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Applications/anaconda3/lib/python3.12/site-packages/zhipuai/core/_http_client.py", line 561, in _request
    response.raise_for_status()
  File "/Applications/anaconda3/lib/python3.12/site-packages/httpx/_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '429 Too Many Requests' for url 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/429

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Applications/anaconda3/lib/python3.12/site-packages/zhipuai/core/_http_client.py", line 561, in _request
    response.raise_for_status()
  File "/Applications/anaconda3/lib/python3.12/site-packages/httpx/_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '429 Too Many Requests' for url 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/429
2025-07-03 16:51:38,990 - zhipuai.core._http_client - DEBUG - Retrying due to status code 429
2025-07-03 16:51:38,990 - zhipuai.core._http_client - DEBUG - 0 retries left
2025-07-03 16:51:38,990 - zhipuai.core._http_client - INFO - Retrying request to /chat/completions in 3.916188 seconds
2025-07-03 16:51:42,910 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 16:51:42,910 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 16:51:42,910 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 16:51:42,912 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 16:51:42,912 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 16:51:58,805 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 03 Jul 2025 08:51:58 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250703165143240f53118340480a'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-03 16:51:58,807 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-03 16:51:58,811 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-03 16:51:58,816 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-03 16:51:58,816 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 16:51:58,816 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-03 16:51:58,816 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-03 16:51:58,824 - __main__ - DEBUG - LLM响应不是JSON格式，返回文本内容: Expecting value: line 1 column 2 (char 1)
2025-07-03 16:51:58,825 - __main__ - DEBUG - 生成有效候选 4/8
2025-07-03 16:51:58,825 - __main__ - DEBUG - 生成候选提示词 5/16
2025-07-03 16:51:58,825 - __main__ - DEBUG - 向模型 glm-4-flash 发送请求...
2025-07-03 16:51:58,825 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-03 16:51:58,825 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-03 16:51:58,826 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-03 16:51:58,827 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-03 16:51:58,827 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-03 16:51:58,827 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-03 16:51:58,827 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-03 16:52:18,230 - httpcore.http11 - DEBUG - receive_response_headers.failed exception=KeyboardInterrupt()
2025-07-03 16:52:18,230 - httpcore.http11 - DEBUG - response_closed.started
2025-07-03 16:52:18,230 - httpcore.http11 - DEBUG - response_closed.complete
